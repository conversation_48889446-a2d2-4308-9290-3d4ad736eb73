﻿#pragma once

#include "GcmpCommandAction.h"
#include "ActionBase.h"
#include "ElementId.h"
#include "Vector3d.h"
#include "ContainerUtil.h"


namespace gcmp
{
    enum EnGbmpPickStatus
    {
        PS_GBMP_NOTSTART = 0,
        PS_GBMP_LBUTTON_DOWN = 1,
        PS_GBMP_LBUTTON_UP = 2,
        PS_GBMP_LBUTTON_MOVING = 4,
        PS_GBMP_LBUTTON_DBCLK = 8
    };

    class IDocument;
    class IElement;
    class IGraphicsNodeReference;
    class IUserTransaction;
    class IPickFilter;
    class IPickEvent;
    class IPickTarget;
    class IPickEventHandler;
    class IPickResult;

    class GbmpPickActionUtil
    {
    public:
        //在视图的给定屏幕位置做一次拾取操作，并更新全局的选择集
        static void UpdateCandidatesSingleton(gcmp::IUiView* pCurrentView,
            int screenX,
            int screenY,
            const gcmp::Vector3d& pos,
            const gcmp::IPickEvent* pPickPostProcesserEvent = nullptr,
            const gcmp::IPickFilter* pPickFilter = nullptr,
            const gcmp::IPickTarget* pPickTarget = nullptr,
            bool isPickingHighlightOnlyGraphicsNodeAllowed = false,
            bool selectByFaceInteriorEnabled = true,
            int pickTolerance = -1,
            bool isHoveringHighlight = true);

        static bool IS_CAD_STYLE_SELECT;
    };


    // 绘制线的动作。
    class GbmpPickActionBase : public gcmp::ActionBase
    {
    public:
        GbmpPickActionBase();
        virtual ~GbmpPickActionBase();

        // 继承自IAction
    public: 
        virtual void InitAction(gcmp::IUiView* pCurrentView) override;

        virtual bool OnLButtonDown(gcmp::IUiView* pCurrentView, const gcmp::Vector3d& pos) override;
        virtual bool OnLButtonUp(gcmp::IUiView* pCurrentView, const gcmp::Vector3d& pos) override;
        virtual bool OnKeyDown(gcmp::IUiView* pCurrentView, int nChar) override;
        virtual bool OnMovePoint(gcmp::IUiView* pCurrentView, const gcmp::Vector3d& pos) override;
        virtual bool OnLButtonDoubleClick(gcmp::IUiView* pCurrentView, const gcmp::Vector3d& pos) override;

        virtual std::wstring GetPromptMessage() const override;

    public:
        void SetIsAllowedMove(bool IsAllowed) { m_isMoveAllowed = IsAllowed; }
        bool IsAllowedMove() const { return m_isMoveAllowed; }

        void SetIsPickingHighlightOnlyGraphicsNodeAllowed(bool IsAllowed) { m_isPickingHighlightOnlyGraphicsNodeAllowed = IsAllowed; }
        bool IsPickingHighlightOnlyGraphicsNodeAllowed() const { return m_isPickingHighlightOnlyGraphicsNodeAllowed; }

        bool IsSelectByFaceInteriorEnabled() const { return m_bSelectByFaceInteriorEnabled; }
        void SetSelectByFaceInteriorEnabled(bool enable) { m_bSelectByFaceInteriorEnabled = enable; }

        bool AddPickPostProcessEvent(gcmp::IPickEventHandler* pPickEvent);
        gcmp::IPickEvent* GetPickPostProcessEvent() const;
        void SetPickPostProcessEvent(OwnerPtr<gcmp::IPickEvent> opPickPostProcessEvent);

        bool AddRectPickPostProcessEvent(gcmp::IPickEventHandler* pPickEvent);
        gcmp::IPickEvent* GetRectPickPostProcessEvent() const;
        void SetRectPickPostProcessEvent(OwnerPtr<gcmp::IPickEvent> opRectPickPostProcessEvent);

        gcmp::IPickTarget* GetPickTarget() { return m_pickTarget.get(); }
        void SetPickTarget(OwnerPtr<gcmp::IPickTarget> pickTarget);

        gcmp::IPickFilter* GetPickFilter() { return m_upPickFilter.get(); }
        void SetPickFilter(OwnerPtr<gcmp::IPickFilter>  opPickFilter);

        int GetPickPixelTolerance() { return m_pickPixelTolerance; }
        void SetPickPixelTolerance(int tolerance);
    protected:
        bool IsDraggingAwaySelectedElementsEnabled(const IUiView* pCurrentView);

        bool UpdateCandidates(gcmp::IUiView* pCurrentView, const gcmp::Vector3d& pos);
        bool IsAuxiliaryElement(const gcmp::IDocument *pDoc, const gcmp::ElementId& id);

        //选择集变化时，给继承类处理的机会
        virtual void OnSelectionChanged();

        virtual void AddToSelection(gcmp::IDocument* pDocument, const gcmp::IGraphicsNodeReference& pickResult, const IUiView* pCurrentView = nullptr);

        virtual void AddToSelectionGroup(gcmp::IDocument* pDoc, gcmp::GraphicsNodeReferenceOwnerPtrSet& pickResults);

        virtual void ClearSelection(gcmp::IDocument* pDoc);

        virtual bool IsSelectionEmpty();

        virtual void ActionCancelled() override;

    protected:
        unsigned int          m_status;
        bool                  m_isCADRectSelectionFlag;
        gcmp::Vector3d        m_startPt;
        gcmp::ElementId       m_auxElementId;//辅助对象Id

        int                   m_pickPixelTolerance;
        gcmp::OwnerPtr<gcmp::IPickTarget>  m_pickTarget;
        gcmp::OwnerPtr<gcmp::IPickFilter>  m_upPickFilter;

        gcmp::OwnerPtr<gcmp::IUserTransaction> m_upUserTransaction;
     
        gcmp::Vector3d m_worldStart;
        std::vector<gcmp::ElementId> m_selectRect;
        gcmp::ElementId m_rectangleSelectID;

        gcmp::OwnerPtr<gcmp::IPickEvent> m_opPickPostProcessEvent;
        gcmp::OwnerPtr<gcmp::IPickEvent> m_opRectPickPostProcessEvent;

        bool m_bSelectByFaceInteriorEnabled;

    private:
        gcmp::OwnerPtr<gcmp::IGraphicsElementShape> CreatSelectRectangleGrep(const gcmp::IUiView* pCurrentView, const gcmp::Vector3d& pos) const;

        void DrawSelectRectangle(gcmp::IUiView* pCurrentView, const gcmp::Vector3d& pos, bool isIntersect);

        void DrawSelectRectangleInReplay(gcmp::IUiView* pCurrentView, const gcmp::Vector3d& pos, bool isIntersect);

        void WorldToScreen(const gcmp::IUiView *pCurrentView, const gcmp::Vector3d &world, gcmp::Vector2d &screen) const;
        //进入编辑模式后，判断当前对象是否可以加入选择集
        bool IsCanBeSelect(gcmp::IDocument* pDoc, const gcmp::IGraphicsNodeReference& refAdd);

        //是否禁用拾取
        bool IsPickVisible();

        void UpdateHighlightOnlySelection();

        std::wstring DisplayElementTriangleNumberOnStatusBar() const;

        void HasTableInSelection(IDocument* pDoc, const GraphicsNodeReferenceOwnerPtrSet& selections, bool& hasTable) const;

        OwnerPtr<gcmp::IPickResult> GetPickResults(gcmp::IUiView* pCurrentView, const gcmp::Vector3d& pos, bool& isReversePick) const;

    private:
        bool m_isMoveAllowed; //是否允许拖拽element
        bool m_isPickingHighlightOnlyGraphicsNodeAllowed; //是否允许高亮可见的对象被pick到
        bool m_isHoveringHighlight; //是否支持略过高亮（默认支持，可以通过配置更改)；
        gcmp::IElement *m_pDebugDrawElement;
    };

}


