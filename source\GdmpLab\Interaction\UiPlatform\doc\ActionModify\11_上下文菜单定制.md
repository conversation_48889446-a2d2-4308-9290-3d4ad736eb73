# ActionModify 上下文菜单定制

## 概述

上下文菜单定制是 ActionModify 系统中的重要扩展机制，允许开发者根据不同的图元类型、选择状态和业务需求动态定制右键菜单。本文档详细分析上下文菜单的定制机制，包括配置文件系统、菜单项工厂、UiAdapter 扩展策略等完整的菜单定制流程。

## 上下文菜单架构

### 1. 菜单定制架构图

```mermaid
classDiagram
    class ICustomizeShortcutMenuManager {
        <<singleton>>
        +Get() ICustomizeShortcutMenuManager*
        +ReadCustomizeShortcutMenu(folder) bool
        +GetShortcutMenuData(element) vector~IMenuItemDefinition~
        +GetShortcutMenuNoSelectData() vector~IMenuItemDefinition~
        +GetDefaultShortcutMenuData() vector~IMenuItemDefinition~
        +FindMenuItem(menuId, element) IMenuItemDefinition*
    }

    class IMenuItemDefinition {
        <<interface>>
        +GetId() wstring
        +GetCaption() wstring
        +GetTooltip() wstring
        +GetIconPath() wstring
    }

    class ICommandMenuItemDefinition {
        <<interface>>
        +GetCommandId() wstring
    }

    class IMenuItemContainerDefinition {
        <<interface>>
        +AddMenuItem(item) void
        +GetMenuItems() vector~IMenuItemDefinition~
    }

    class ControlDefinitionFactory {
        +CreateCommandMenuItem(id, cmdId, caption, tooltip, icon) OwnerPtr~ICommandMenuItemDefinition~
        +CreateMenuItemContainer(id, caption) OwnerPtr~IMenuItemContainerDefinition~
        +CreateSeparator() OwnerPtr~IMenuItemDefinition~
    }

    class IElementContextMenuStrategy {
        <<interface>>
        +AppendContextMenu(element, container) bool
    }

    class UiAdapter {
        +RegisterContextMenuStrategy(strategy) void
        +ApplyContextMenuStrategy(doc, elementId, container) void
    }

    class GbmpModifyElementsBehavior {
        +PrepareContextMenu(view) OwnerPtr~IMenuItemContainerDefinition~
    }

    ICustomizeShortcutMenuManager --> IMenuItemDefinition : creates
    IMenuItemDefinition <|-- ICommandMenuItemDefinition
    IMenuItemDefinition <|-- IMenuItemContainerDefinition
    ControlDefinitionFactory --> IMenuItemDefinition : creates
    UiAdapter --> IElementContextMenuStrategy : manages
    GbmpModifyElementsBehavior --> ICustomizeShortcutMenuManager : uses
    GbmpModifyElementsBehavior --> UiAdapter : uses
```

### 2. 菜单定制流程图

```mermaid
flowchart TD
    A[右键点击] --> B[PrepareContextMenu]
    B --> C[读取配置文件]
    C --> D{选择状态}

    D -->|单选| E[GetShortcutMenuData]
    D -->|无选择| F[GetShortcutMenuNoSelectData]
    D -->|多选| G[GetDefaultShortcutMenuData]

    E --> H[应用UiAdapter策略]
    F --> I[创建基础菜单]
    G --> J[添加特殊菜单项]

    H --> K[添加表格子菜单]
    I --> K
    J --> K

    K --> L[构建最终菜单]
    L --> M[显示上下文菜单]
```

## 配置文件系统

### 1. 配置文件读取

```cpp
// 配置文件读取入口
bool LoadMenuConfiguration()
{
    // 读取自定义右键菜单配置
    bool success = ICustomizeShortcutMenuManager::Get()->ReadCustomizeShortcutMenu(
        L"shortcutMenu_config");

    if (!success)
    {
        DBG_WARN(L"读取右键菜单配置失败", L"GDMPLab", L"2024-01-15");
        return false;
    }

    return true;
}

// 配置文件路径结构
// {应用程序目录}/shortcutMenu_config/CustomizeShortcutMenu.xml
```

### 2. 配置文件格式

```xml
<!-- CustomizeShortcutMenu.xml 示例 -->
<?xml version="1.0" encoding="utf-8"?>
<CustomizeShortcutMenu>
    <!-- 图元特定菜单 -->
    <ElementMenus>
        <ElementMenu elementType="Wall">
            <MenuItem id="wall_edit" commandId="CMD_EDIT_WALL"
                     caption="编辑墙" tooltip="编辑墙属性" icon="wall_edit.png"/>
            <MenuItem id="wall_delete" commandId="CMD_DELETE_WALL"
                     caption="删除墙" tooltip="删除选中的墙" icon="delete.png"/>
            <Separator/>
            <SubMenu id="wall_tools" caption="墙工具">
                <MenuItem id="wall_split" commandId="CMD_SPLIT_WALL"
                         caption="分割墙" tooltip="分割墙体" icon="split.png"/>
                <MenuItem id="wall_join" commandId="CMD_JOIN_WALL"
                         caption="连接墙" tooltip="连接墙体" icon="join.png"/>
            </SubMenu>
        </ElementMenu>

        <ElementMenu elementType="Beam">
            <MenuItem id="beam_edit" commandId="CMD_EDIT_BEAM"
                     caption="编辑梁" tooltip="编辑梁属性" icon="beam_edit.png"/>
            <MenuItem id="beam_delete" commandId="CMD_DELETE_BEAM"
                     caption="删除梁" tooltip="删除选中的梁" icon="delete.png"/>
        </ElementMenu>
    </ElementMenus>

    <!-- 无选择时的菜单 -->
    <NoSelectionMenu>
        <MenuItem id="create_wall" commandId="CMD_CREATE_WALL"
                 caption="创建墙" tooltip="创建新墙" icon="create_wall.png"/>
        <MenuItem id="create_beam" commandId="CMD_CREATE_BEAM"
                 caption="创建梁" tooltip="创建新梁" icon="create_beam.png"/>
        <Separator/>
        <MenuItem id="view_properties" commandId="CMD_VIEW_PROPERTIES"
                 caption="视图属性" tooltip="查看视图属性" icon="properties.png"/>
    </NoSelectionMenu>

    <!-- 多选时的默认菜单 -->
    <DefaultMenu>
        <MenuItem id="delete_selected" commandId="CMD_DELETE_SELECTED"
                 caption="删除选中" tooltip="删除所有选中对象" icon="delete.png"/>
        <MenuItem id="group_selected" commandId="CMD_GROUP_SELECTED"
                 caption="成组" tooltip="将选中对象成组" icon="group.png"/>
        <Separator/>
        <MenuItem id="copy_selected" commandId="CMD_COPY_SELECTED"
                 caption="复制" tooltip="复制选中对象" icon="copy.png"/>
        <MenuItem id="move_selected" commandId="CMD_MOVE_SELECTED"
                 caption="移动" tooltip="移动选中对象" icon="move.png"/>
    </DefaultMenu>
</CustomizeShortcutMenu>
```

### 3. 配置文件解析

```cpp
class CustomizeShortcutMenuParser
{
public:
    bool ParseConfigurationFile(const std::wstring& configPath)
    {
        // 1. 加载XML文档
        XMLDocument doc;
        if (doc.LoadFile(WStringToString(configPath).c_str()) != XML_SUCCESS)
        {
            return false;
        }

        // 2. 解析根节点
        XMLElement* pRoot = doc.FirstChildElement("CustomizeShortcutMenu");
        if (!pRoot)
            return false;

        // 3. 解析图元菜单
        ParseElementMenus(pRoot->FirstChildElement("ElementMenus"));

        // 4. 解析无选择菜单
        ParseNoSelectionMenu(pRoot->FirstChildElement("NoSelectionMenu"));

        // 5. 解析默认菜单
        ParseDefaultMenu(pRoot->FirstChildElement("DefaultMenu"));

        return true;
    }

private:
    void ParseElementMenus(XMLElement* pElementMenus)
    {
        if (!pElementMenus)
            return;

        for (XMLElement* pElementMenu = pElementMenus->FirstChildElement("ElementMenu");
             pElementMenu;
             pElementMenu = pElementMenu->NextSiblingElement("ElementMenu"))
        {
            std::wstring elementType = StringToWString(
                pElementMenu->Attribute("elementType"));

            std::vector<OwnerPtr<IMenuItemDefinition>> menuItems;
            ParseMenuItems(pElementMenu, menuItems);

            m_elementMenus[elementType] = std::move(menuItems);
        }
    }

    void ParseMenuItems(XMLElement* pParent,
                       std::vector<OwnerPtr<IMenuItemDefinition>>& menuItems)
    {
        for (XMLElement* pChild = pParent->FirstChildElement();
             pChild;
             pChild = pChild->NextSiblingElement())
        {
            std::string nodeName = pChild->Name();

            if (nodeName == "MenuItem")
            {
                OwnerPtr<ICommandMenuItemDefinition> pMenuItem =
                    CreateMenuItemFromXML(pChild);
                if (pMenuItem)
                {
                    menuItems.push_back(TransferOwnership(pMenuItem));
                }
            }
            else if (nodeName == "Separator")
            {
                OwnerPtr<IMenuItemDefinition> pSeparator =
                    ControlDefinitionFactory::CreateSeparator();
                menuItems.push_back(TransferOwnership(pSeparator));
            }
            else if (nodeName == "SubMenu")
            {
                OwnerPtr<IMenuItemContainerDefinition> pSubMenu =
                    CreateSubMenuFromXML(pChild);
                if (pSubMenu)
                {
                    menuItems.push_back(TransferOwnership(pSubMenu));
                }
            }
        }
    }

    OwnerPtr<ICommandMenuItemDefinition> CreateMenuItemFromXML(XMLElement* pElement)
    {
        std::wstring id = StringToWString(pElement->Attribute("id"));
        std::wstring commandId = StringToWString(pElement->Attribute("commandId"));
        std::wstring caption = StringToWString(pElement->Attribute("caption"));
        std::wstring tooltip = StringToWString(pElement->Attribute("tooltip"));
        std::wstring icon = StringToWString(pElement->Attribute("icon"));

        return ControlDefinitionFactory::CreateCommandMenuItem(
            id, commandId, caption, tooltip, icon);
    }

private:
    std::map<std::wstring, std::vector<OwnerPtr<IMenuItemDefinition>>> m_elementMenus;
    std::vector<OwnerPtr<IMenuItemDefinition>> m_noSelectionMenu;
    std::vector<OwnerPtr<IMenuItemDefinition>> m_defaultMenu;
};
```

## 菜单项工厂系统

### 1. ControlDefinitionFactory 菜单项创建

```cpp
class ControlDefinitionFactory : public IControlDefinitionFactory
{
public:
    // 创建命令菜单项
    virtual OwnerPtr<ICommandMenuItemDefinition> CreateCommandMenuItem(
        const std::wstring& id,
        const std::wstring& commandId,
        const std::wstring& caption,
        const std::wstring& tooltip,
        const std::wstring& iconPath) override
    {
        return NEW_AS_OWNER_PTR(CommandMenuItemDefinition,
                               id, commandId, caption, tooltip, iconPath);
    }

    // 创建菜单容器
    virtual OwnerPtr<IMenuItemContainerDefinition> CreateMenuItemContainer(
        const std::wstring& id,
        const std::wstring& caption) override
    {
        return NEW_AS_OWNER_PTR(MenuItemContainerDefinition, id, caption);
    }

    // 创建分隔符
    virtual OwnerPtr<IMenuItemDefinition> CreateSeparator() override
    {
        return NEW_AS_OWNER_PTR(SeparatorItemDefinition);
    }

    // 创建菜单项引用
    virtual OwnerPtr<ICommandMenuItemDefinition> CreateCommandMenuItemReference(
        const ICommandMenuItemDefinition* pCommandMenuItemDefinition) override
    {
        const CommandMenuItemDefinition* pDefinition =
            dynamic_cast<const CommandMenuItemDefinition*>(pCommandMenuItemDefinition);

        if (!pDefinition)
            return nullptr;

        // 验证是否已在库中注册
        ICommandMenuItemDefinition* pLibraryDefinition =
            ControlDefinitionLibraryUtil::GetControlDefinition<CommandMenuItemDefinition>(
                pCommandMenuItemDefinition->GetId());

        if (pLibraryDefinition != pCommandMenuItemDefinition)
            return nullptr;

        return NEW_AS_OWNER_PTR(CommandMenuItemDefinitionReference,
                               const_cast<CommandMenuItemDefinition*>(pDefinition));
    }
};
```

### 2. 菜单项定义实现

```cpp
class CommandMenuItemDefinition : public ICommandMenuItemDefinition
{
public:
    CommandMenuItemDefinition(
        const std::wstring& id,
        const std::wstring& commandId,
        const std::wstring& caption,
        const std::wstring& tooltip,
        const std::wstring& iconPath)
        : m_controlDefine(NEW_AS_OWNER_PTR(CommandControlDefinition,
                                          id, caption, tooltip, commandId))
    {
        // 处理图标路径
        m_iconPath = iconPath.empty() ? iconPath :
                    FilePath::GetAbsolutePath(iconPath, FileSystem::GetExeDirPath());
    }

    virtual std::wstring GetId() const override
    {
        return m_controlDefine->GetId();
    }

    virtual std::wstring GetCaption() const override
    {
        return m_controlDefine->GetCaption();
    }

    virtual std::wstring GetTooltip() const override
    {
        return m_controlDefine->GetTooltip();
    }

    virtual std::wstring GetCommandId() const override
    {
        return m_controlDefine->GetCommandId();
    }

    virtual std::wstring GetIconPath() const override
    {
        return m_iconPath;
    }

private:
    OwnerPtr<CommandControlDefinition> m_controlDefine;
    std::wstring m_iconPath;
};

class MenuItemContainerDefinition : public IMenuItemContainerDefinition
{
public:
    MenuItemContainerDefinition(const std::wstring& id, const std::wstring& caption)
        : m_id(id), m_caption(caption)
    {
    }

    virtual void AddMenuItem(OwnerPtr<IMenuItemDefinition> pMenuItem) override
    {
        if (pMenuItem)
        {
            m_menuItems.push_back(TransferOwnership(pMenuItem));
        }
    }

    virtual const std::vector<OwnerPtr<IMenuItemDefinition>>& GetMenuItems() const override
    {
        return m_menuItems;
    }

    virtual std::wstring GetId() const override { return m_id; }
    virtual std::wstring GetCaption() const override { return m_caption; }

private:
    std::wstring m_id;
    std::wstring m_caption;
    std::vector<OwnerPtr<IMenuItemDefinition>> m_menuItems;
};
```

## UiAdapter 扩展策略

### 1. IElementContextMenuStrategy 接口

```cpp
namespace Sample
{
    // 图元上下文菜单策略接口
    class IElementContextMenuStrategy
    {
    public:
        virtual ~IElementContextMenuStrategy() = default;

        // 为单个图元添加上下文菜单
        virtual bool AppendContextMenu(
            gcmp::IElement* pElement,
            gcmp::IMenuItemContainerDefinition* pMenuItemContainer) const = 0;

        // 为多个图元添加上下文菜单
        virtual bool AppendContextMenu(
            std::unordered_set<gcmp::IElement*> elements,
            gcmp::IMenuItemContainerDefinition* pMenuItemContainer) const
        {
            return false;
        }
    };
}
```

### 2. UiAdapter 管理器

```cpp
class UiAdapter
{
public:
    // 注册上下文菜单策略
    static void RegisterContextMenuStrategy(
        OwnerPtr<Sample::IElementContextMenuStrategy> opStrategy)
    {
        GetModifyer().m_objects.emplace_back(TransferOwnership(opStrategy));
    }

    // 应用上下文菜单策略
    static void ApplyContextMenuStrategy(
        IDocument* pDocument,
        ElementId elementId,
        IMenuItemContainerDefinition* pMenuItemContainer)
    {
        GetModifyer().AppendContextMenu(pDocument, elementId, pMenuItemContainer);
    }

private:
    class Modifyer
    {
    public:
        static Modifyer& Get()
        {
            static Modifyer object{};
            return object;
        }

        bool AppendContextMenu(
            IDocument* pDocument,
            ElementId elementId,
            IMenuItemContainerDefinition* pMenuItemContainer) const
        {
            if (!pDocument || !pMenuItemContainer)
                return false;

            IElement* pElement = pDocument->GetElement(elementId);
            if (!pElement)
                return false;

            // 遍历所有注册的策略
            for (auto& strategy : m_objects)
            {
                if (strategy && strategy->AppendContextMenu(pElement, pMenuItemContainer))
                {
                    return true;
                }
            }

            return false;
        }

        std::vector<OwnerPtr<IElementContextMenuStrategy>> m_objects;
    };

    static Modifyer& GetModifyer() { return Modifyer::Get(); }
};
```

### 3. 自定义菜单策略实现

```cpp
class WallContextMenuStrategy : public Sample::IElementContextMenuStrategy
{
public:
    virtual bool AppendContextMenu(
        gcmp::IElement* pElement,
        gcmp::IMenuItemContainerDefinition* pMenuItemContainer) const override
    {
        // 检查是否为墙图元
        const IWall* pWall = quick_cast<IWall>(pElement);
        if (!pWall)
            return false;

        // 添加墙特定的菜单项
        AddWallSpecificMenuItems(pWall, pMenuItemContainer);

        return true;
    }

private:
    void AddWallSpecificMenuItems(
        const IWall* pWall,
        IMenuItemContainerDefinition* pMenuItemContainer) const
    {
        // 1. 添加分隔符
        OwnerPtr<IMenuItemDefinition> pSeparator =
            ControlDefinitionFactory::CreateSeparator();
        pMenuItemContainer->AddMenuItem(TransferOwnership(pSeparator));

        // 2. 添加墙编辑菜单
        OwnerPtr<ICommandMenuItemDefinition> pEditWall =
            ControlDefinitionFactory::CreateCommandMenuItem(
                L"wall_edit_custom",
                L"CMD_EDIT_WALL_PROPERTIES",
                L"编辑墙属性",
                L"打开墙属性编辑对话框",
                L"icons/wall_edit.png"
            );
        pMenuItemContainer->AddMenuItem(TransferOwnership(pEditWall));

        // 3. 根据墙的状态添加条件菜单
        if (CanSplitWall(pWall))
        {
            OwnerPtr<ICommandMenuItemDefinition> pSplitWall =
                ControlDefinitionFactory::CreateCommandMenuItem(
                    L"wall_split_custom",
                    L"CMD_SPLIT_WALL",
                    L"分割墙",
                    L"在指定位置分割墙体",
                    L"icons/wall_split.png"
                );
            pMenuItemContainer->AddMenuItem(TransferOwnership(pSplitWall));
        }

        // 4. 添加墙工具子菜单
        AddWallToolsSubMenu(pWall, pMenuItemContainer);
    }

    void AddWallToolsSubMenu(
        const IWall* pWall,
        IMenuItemContainerDefinition* pMenuItemContainer) const
    {
        // 创建子菜单容器
        OwnerPtr<IMenuItemContainerDefinition> pSubMenu =
            ControlDefinitionFactory::CreateMenuItemContainer(
                L"wall_tools_submenu", L"墙工具");

        // 添加子菜单项
        OwnerPtr<ICommandMenuItemDefinition> pFlipWall =
            ControlDefinitionFactory::CreateCommandMenuItem(
                L"wall_flip",
                L"CMD_FLIP_WALL",
                L"翻转墙",
                L"翻转墙的方向",
                L"icons/wall_flip.png"
            );
        pSubMenu->AddMenuItem(TransferOwnership(pFlipWall));

        OwnerPtr<ICommandMenuItemDefinition> pExtendWall =
            ControlDefinitionFactory::CreateCommandMenuItem(
                L"wall_extend",
                L"CMD_EXTEND_WALL",
                L"延伸墙",
                L"延伸墙到指定位置",
                L"icons/wall_extend.png"
            );
        pSubMenu->AddMenuItem(TransferOwnership(pExtendWall));

        // 将子菜单添加到主菜单
        pMenuItemContainer->AddMenuItem(TransferOwnership(pSubMenu));
    }

    bool CanSplitWall(const IWall* pWall) const
    {
        // 检查墙是否可以分割的业务逻辑
        return pWall->GetLength() > 1000.0; // 示例：长度大于1米才能分割
    }
};
```

## 菜单集成与应用

### 1. ActionModify 中的菜单集成

```cpp
OwnerPtr<IMenuItemContainerDefinition> GbmpModifyElementsBehavior::PrepareContextMenu(
    IUiView* pCurrentView)
{
    IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();

    // 1. 创建菜单容器
    OwnerPtr<IMenuItemContainerDefinition> pMenuItemContainer =
        ControlDefinitionFactory::CreateMenuItemContainer(L"", L"");

    // 2. 获取拾取候选对象
    std::vector<OwnerPtr<IPickCandidate>> pickCandidate = GetPickCandidate(pCurrentView);

    // 3. 读取配置文件
    ICustomizeShortcutMenuManager::Get()->ReadCustomizeShortcutMenu(L"shortcutMenu_config");

    // 4. 根据选择状态构建菜单
    if (pickCandidate.size() == 1)
    {
        // 单选状态
        BuildSingleSelectionMenu(pDoc, pickCandidate[0].get(), pMenuItemContainer.get());
    }
    else if (ISelection::Get()->IsEmpty())
    {
        // 无选择状态
        BuildNoSelectionMenu(pMenuItemContainer.get());
    }
    else
    {
        // 多选状态
        BuildMultiSelectionMenu(pCurrentView, pMenuItemContainer.get());
    }

    return pMenuItemContainer;
}

void BuildSingleSelectionMenu(
    IDocument* pDoc,
    IPickCandidate* pPickCandidate,
    IMenuItemContainerDefinition* pMenuItemContainer)
{
    ElementId elementId = pPickCandidate->GetElementId();
    IElement* pElement = pDoc->GetElement(elementId);

    if (!pElement)
        return;

    // 1. 获取基础菜单
    std::vector<OwnerPtr<IMenuItemDefinition>> menus =
        ICustomizeShortcutMenuManager::Get()->GetShortcutMenuData(pElement);

    // 2. 添加基础菜单项
    for (auto& menu : menus)
    {
        pMenuItemContainer->AddMenuItem(TransferOwnership(menu));
    }

    // 3. 应用UiAdapter扩展策略
    #ifdef UiAdapter_IMPL
    Sample::UiAdapter::ApplyContextMenuStrategy(pDoc, elementId, pMenuItemContainer);
    #endif

    // 4. 添加通用操作菜单
    AddCommonOperationMenus(pElement, pMenuItemContainer);
}

void BuildNoSelectionMenu(IMenuItemContainerDefinition* pMenuItemContainer)
{
    // 获取无选择时的菜单
    std::vector<OwnerPtr<IMenuItemDefinition>> menus =
        ICustomizeShortcutMenuManager::Get()->GetShortcutMenuNoSelectData();

    for (auto& menu : menus)
    {
        pMenuItemContainer->AddMenuItem(TransferOwnership(menu));
    }
}

void BuildMultiSelectionMenu(
    IUiView* pCurrentView,
    IMenuItemContainerDefinition* pMenuItemContainer)
{
    // 1. 获取默认多选菜单
    std::vector<OwnerPtr<IMenuItemDefinition>> menus =
        ICustomizeShortcutMenuManager::Get()->GetDefaultShortcutMenuData();

    for (auto& menu : menus)
    {
        pMenuItemContainer->AddMenuItem(TransferOwnership(menu));
    }

    // 2. 添加在位编辑菜单（单选时）
    if (ISelection::Get()->GetCount() == 1)
    {
        AddInPlaceEditMenu(pMenuItemContainer);
    }

    // 3. 添加表格子菜单
    if (IsNeedShowTableSubMenu(pCurrentView))
    {
        AddTableSubMenu(pCurrentView, pMenuItemContainer);
    }
}
```

### 2. 表格菜单定制

```cpp
void AddTableSubMenu(
    IUiView* pCurrentView,
    IMenuItemContainerDefinition* pMenuItemContainer)
{
    #ifndef UiAdapter_IMPL
    // 传统方式添加表格菜单
    AddDrawingTableMenuItem(pMenuItemContainer);
    #else
    // 使用UiAdapter策略
    std::vector<OwnerPtr<IPickCandidate>> pickCandidate = GetPickCandidate(pCurrentView);
    if (!pickCandidate.empty())
    {
        IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();
        Sample::UiAdapter::ApplyContextMenuStrategy(
            pDoc,
            pickCandidate[0]->GetElementId(),
            pMenuItemContainer
        );
    }
    #endif
}

void AddDrawingTableMenuItem(IMenuItemContainerDefinition* pMenuItemContainer)
{
    // 创建表格子菜单
    OwnerPtr<IMenuItemContainerDefinition> pTableSubMenu =
        ControlDefinitionFactory::CreateMenuItemContainer(
            L"table_submenu", L"表格操作");

    // 添加表格相关菜单项
    OwnerPtr<ICommandMenuItemDefinition> pInsertRow =
        ControlDefinitionFactory::CreateCommandMenuItem(
            L"table_insert_row",
            L"CMD_TABLE_INSERT_ROW",
            L"插入行",
            L"在当前位置插入新行",
            L"icons/table_insert_row.png"
        );
    pTableSubMenu->AddMenuItem(TransferOwnership(pInsertRow));

    OwnerPtr<ICommandMenuItemDefinition> pInsertColumn =
        ControlDefinitionFactory::CreateCommandMenuItem(
            L"table_insert_column",
            L"CMD_TABLE_INSERT_COLUMN",
            L"插入列",
            L"在当前位置插入新列",
            L"icons/table_insert_column.png"
        );
    pTableSubMenu->AddMenuItem(TransferOwnership(pInsertColumn));

    OwnerPtr<ICommandMenuItemDefinition> pDeleteRow =
        ControlDefinitionFactory::CreateCommandMenuItem(
            L"table_delete_row",
            L"CMD_TABLE_DELETE_ROW",
            L"删除行",
            L"删除当前行",
            L"icons/table_delete_row.png"
        );
    pTableSubMenu->AddMenuItem(TransferOwnership(pDeleteRow));

    OwnerPtr<ICommandMenuItemDefinition> pDeleteColumn =
        ControlDefinitionFactory::CreateCommandMenuItem(
            L"table_delete_column",
            L"CMD_TABLE_DELETE_COLUMN",
            L"删除列",
            L"删除当前列",
            L"icons/table_delete_column.png"
        );
    pTableSubMenu->AddMenuItem(TransferOwnership(pDeleteColumn));

    // 将表格子菜单添加到主菜单
    pMenuItemContainer->AddMenuItem(TransferOwnership(pTableSubMenu));
}

bool IsNeedShowTableSubMenu(IUiView* pCurrentView)
{
    // 检查当前选择是否包含表格元素
    const GraphicsNodeReferenceOwnerPtrSet& selections =
        ISelection::Get()->GetGraphicsNodeReferences();

    for (const auto& selection : selections)
    {
        IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();
        IElement* pElement = pDoc->GetElement(selection->GetElementId());

        if (IsTableElement(pElement))
        {
            return true;
        }
    }

    return false;
}
```

### 3. 在位编辑菜单

```cpp
void AddInPlaceEditMenu(IMenuItemContainerDefinition* pMenuItemContainer)
{
    std::wstring strCmdId = ID_CMD_ENTER_INPLACE_EDIT_MODE;

    // 查找在位编辑菜单项
    const IMenuItemDefinition* pInPlaceEditMenuItem =
        ICustomizeShortcutMenuManager::Get()->FindMenuItem(
            L"inplace_edit", nullptr);

    if (pInPlaceEditMenuItem)
    {
        // 使用配置的菜单项
        OwnerPtr<IMenuItemDefinition> pMenuItem =
            CloneMenuItemDefinition(pInPlaceEditMenuItem);
        pMenuItemContainer->AddMenuItem(TransferOwnership(pMenuItem));
    }
    else
    {
        // 创建默认在位编辑菜单项
        OwnerPtr<ICommandMenuItemDefinition> pInPlaceEdit =
            ControlDefinitionFactory::CreateCommandMenuItem(
                L"inplace_edit_default",
                strCmdId,
                L"在位编辑",
                L"进入在位编辑模式",
                L"icons/inplace_edit.png"
            );
        pMenuItemContainer->AddMenuItem(TransferOwnership(pInPlaceEdit));
    }
}
```

## 高级菜单定制

### 1. 动态菜单生成

```cpp
class DynamicMenuGenerator
{
public:
    static void GenerateElementSpecificMenu(
        const IElement* pElement,
        IMenuItemContainerDefinition* pMenuItemContainer)
    {
        // 根据图元类型动态生成菜单
        if (const IWall* pWall = quick_cast<IWall>(pElement))
        {
            GenerateWallMenu(pWall, pMenuItemContainer);
        }
        else if (const IBeam* pBeam = quick_cast<IBeam>(pElement))
        {
            GenerateBeamMenu(pBeam, pMenuItemContainer);
        }
        else if (const IColumn* pColumn = quick_cast<IColumn>(pElement))
        {
            GenerateColumnMenu(pColumn, pMenuItemContainer);
        }
        else if (const IFloor* pFloor = quick_cast<IFloor>(pElement))
        {
            GenerateFloorMenu(pFloor, pMenuItemContainer);
        }
        // 其他图元类型...
    }

private:
    static void GenerateWallMenu(
        const IWall* pWall,
        IMenuItemContainerDefinition* pMenuItemContainer)
    {
        // 根据墙的属性动态生成菜单
        WallType wallType = pWall->GetWallType();
        double wallHeight = pWall->GetHeight();
        bool isLoadBearing = pWall->IsLoadBearing();

        // 基础编辑菜单
        AddBasicEditMenus(pMenuItemContainer);

        // 根据墙类型添加特定菜单
        switch (wallType)
        {
        case WallType::Curtain:
            AddCurtainWallMenus(pMenuItemContainer);
            break;
        case WallType::Structural:
            AddStructuralWallMenus(pMenuItemContainer);
            break;
        case WallType::Partition:
            AddPartitionWallMenus(pMenuItemContainer);
            break;
        }

        // 根据墙高度添加条件菜单
        if (wallHeight > 3000.0) // 高度大于3米
        {
            AddHighWallMenus(pMenuItemContainer);
        }

        // 承重墙特殊菜单
        if (isLoadBearing)
        {
            AddLoadBearingWallMenus(pMenuItemContainer);
        }
    }

    static void AddCurtainWallMenus(IMenuItemContainerDefinition* pMenuItemContainer)
    {
        OwnerPtr<ICommandMenuItemDefinition> pEditPanels =
            ControlDefinitionFactory::CreateCommandMenuItem(
                L"curtain_wall_edit_panels",
                L"CMD_EDIT_CURTAIN_PANELS",
                L"编辑面板",
                L"编辑幕墙面板",
                L"icons/curtain_panels.png"
            );
        pMenuItemContainer->AddMenuItem(TransferOwnership(pEditPanels));

        OwnerPtr<ICommandMenuItemDefinition> pEditMullions =
            ControlDefinitionFactory::CreateCommandMenuItem(
                L"curtain_wall_edit_mullions",
                L"CMD_EDIT_CURTAIN_MULLIONS",
                L"编辑竖梃",
                L"编辑幕墙竖梃",
                L"icons/curtain_mullions.png"
            );
        pMenuItemContainer->AddMenuItem(TransferOwnership(pEditMullions));
    }
};
```

### 2. 条件菜单显示

```cpp
class ConditionalMenuManager
{
public:
    static bool ShouldShowMenuItem(
        const IMenuItemDefinition* pMenuItem,
        const IElement* pElement,
        const IUiView* pCurrentView)
    {
        // 获取菜单项的显示条件
        std::wstring menuId = pMenuItem->GetId();

        // 检查图元相关条件
        if (!CheckElementConditions(menuId, pElement))
            return false;

        // 检查视图相关条件
        if (!CheckViewConditions(menuId, pCurrentView))
            return false;

        // 检查权限条件
        if (!CheckPermissionConditions(menuId, pElement))
            return false;

        // 检查业务逻辑条件
        if (!CheckBusinessConditions(menuId, pElement))
            return false;

        return true;
    }

private:
    static bool CheckElementConditions(
        const std::wstring& menuId,
        const IElement* pElement)
    {
        if (!pElement)
            return false;

        // 检查图元类型条件
        if (menuId.find(L"wall_") == 0)
        {
            return quick_cast<IWall>(pElement) != nullptr;
        }
        else if (menuId.find(L"beam_") == 0)
        {
            return quick_cast<IBeam>(pElement) != nullptr;
        }
        // 其他类型检查...

        return true;
    }

    static bool CheckViewConditions(
        const std::wstring& menuId,
        const IUiView* pCurrentView)
    {
        if (!pCurrentView)
            return false;

        // 检查视图类型条件
        IModelView* pModelView = pCurrentView->GetCanvas()->GetModelView();
        if (!pModelView)
            return false;

        ViewType viewType = pModelView->GetViewType();

        // 某些菜单只在特定视图中显示
        if (menuId == L"section_view_only_menu")
        {
            return viewType == ViewType::Section;
        }
        else if (menuId == L"plan_view_only_menu")
        {
            return viewType == ViewType::Plan;
        }

        return true;
    }

    static bool CheckPermissionConditions(
        const std::wstring& menuId,
        const IElement* pElement)
    {
        // 检查用户权限
        if (menuId.find(L"delete_") == 0)
        {
            return HasDeletePermission(pElement);
        }
        else if (menuId.find(L"edit_") == 0)
        {
            return HasEditPermission(pElement);
        }

        return true;
    }

    static bool CheckBusinessConditions(
        const std::wstring& menuId,
        const IElement* pElement)
    {
        // 检查业务逻辑条件
        if (menuId == L"wall_split")
        {
            const IWall* pWall = quick_cast<IWall>(pElement);
            return pWall && pWall->GetLength() > 1000.0; // 长度大于1米才能分割
        }
        else if (menuId == L"beam_extend")
        {
            const IBeam* pBeam = quick_cast<IBeam>(pElement);
            return pBeam && !pBeam->IsConnectedAtBothEnds(); // 两端都连接时不能延伸
        }

        return true;
    }

    static bool HasDeletePermission(const IElement* pElement)
    {
        // 检查删除权限的具体实现
        return pElement && !pElement->IsLocked() && pElement->CanBeDeleted();
    }

    static bool HasEditPermission(const IElement* pElement)
    {
        // 检查编辑权限的具体实现
        return pElement && !pElement->IsReadOnly() && pElement->CanBeModified();
    }
};
```

## 菜单本地化支持

### 1. 多语言菜单配置

```cpp
class LocalizedMenuManager
{
public:
    static void LoadLocalizedMenus(const std::wstring& language)
    {
        // 根据语言加载对应的菜单配置
        std::wstring configPath = GetLocalizedConfigPath(language);

        if (FileSystem::FileExists(configPath))
        {
            ICustomizeShortcutMenuManager::Get()->ReadCustomizeShortcutMenu(
                GetConfigFolderName(language));
        }
        else
        {
            // 回退到默认语言
            ICustomizeShortcutMenuManager::Get()->ReadCustomizeShortcutMenu(
                L"shortcutMenu_config");
        }
    }

    static std::wstring GetLocalizedText(
        const std::wstring& key,
        const std::wstring& defaultText)
    {
        // 从资源文件或配置文件获取本地化文本
        auto it = s_localizedTexts.find(key);
        if (it != s_localizedTexts.end())
        {
            return it->second;
        }

        return defaultText;
    }

private:
    static std::wstring GetLocalizedConfigPath(const std::wstring& language)
    {
        return FileSystem::GetExeDirPath() + L"/config/" + language +
               L"/CustomizeShortcutMenu.xml";
    }

    static std::wstring GetConfigFolderName(const std::wstring& language)
    {
        return L"shortcutMenu_config_" + language;
    }

    static std::map<std::wstring, std::wstring> s_localizedTexts;
};
```

### 2. 动态文本更新

```cpp
class DynamicMenuTextProvider
{
public:
    static std::wstring GetDynamicMenuText(
        const std::wstring& menuId,
        const IElement* pElement)
    {
        // 根据图元状态动态生成菜单文本
        if (menuId == L"element_info")
        {
            return GenerateElementInfoText(pElement);
        }
        else if (menuId == L"element_properties")
        {
            return GeneratePropertiesText(pElement);
        }

        return L"";
    }

private:
    static std::wstring GenerateElementInfoText(const IElement* pElement)
    {
        if (!pElement)
            return L"图元信息";

        std::wstring elementType = GetElementTypeName(pElement);
        ElementId elementId = pElement->GetElementId();

        return elementType + L" (ID: " + elementId.ToString() + L")";
    }

    static std::wstring GeneratePropertiesText(const IElement* pElement)
    {
        if (!pElement)
            return L"属性";

        std::wstring elementType = GetElementTypeName(pElement);
        return L"编辑" + elementType + L"属性";
    }

    static std::wstring GetElementTypeName(const IElement* pElement)
    {
        if (quick_cast<IWall>(pElement))
            return L"墙";
        else if (quick_cast<IBeam>(pElement))
            return L"梁";
        else if (quick_cast<IColumn>(pElement))
            return L"柱";
        else if (quick_cast<IFloor>(pElement))
            return L"楼板";

        return L"图元";
    }
};
```

## 菜单性能优化

### 1. 菜单缓存机制

```cpp
class MenuCacheManager
{
public:
    static OwnerPtr<IMenuItemContainerDefinition> GetCachedMenu(
        const std::wstring& cacheKey)
    {
        auto it = s_menuCache.find(cacheKey);
        if (it != s_menuCache.end())
        {
            return CloneMenuContainer(it->second.get());
        }

        return nullptr;
    }

    static void CacheMenu(
        const std::wstring& cacheKey,
        OwnerPtr<IMenuItemContainerDefinition> pMenu)
    {
        if (s_menuCache.size() >= MAX_CACHE_SIZE)
        {
            // 清理最旧的缓存项
            ClearOldestCacheItem();
        }

        s_menuCache[cacheKey] = CloneMenuContainer(pMenu.get());
    }

    static void ClearCache()
    {
        s_menuCache.clear();
    }

private:
    static const size_t MAX_CACHE_SIZE = 100;
    static std::map<std::wstring, OwnerPtr<IMenuItemContainerDefinition>> s_menuCache;

    static void ClearOldestCacheItem()
    {
        if (!s_menuCache.empty())
        {
            s_menuCache.erase(s_menuCache.begin());
        }
    }

    static OwnerPtr<IMenuItemContainerDefinition> CloneMenuContainer(
        const IMenuItemContainerDefinition* pOriginal);
};
```

### 2. 延迟菜单构建

```cpp
class LazyMenuBuilder
{
public:
    static OwnerPtr<IMenuItemContainerDefinition> BuildMenuLazily(
        const IElement* pElement,
        const IUiView* pCurrentView)
    {
        // 创建延迟菜单容器
        OwnerPtr<LazyMenuItemContainer> pLazyContainer =
            NEW_AS_OWNER_PTR(LazyMenuItemContainer, pElement, pCurrentView);

        return TransferOwnership(pLazyContainer);
    }
};

class LazyMenuItemContainer : public IMenuItemContainerDefinition
{
public:
    LazyMenuItemContainer(const IElement* pElement, const IUiView* pCurrentView)
        : m_pElement(pElement), m_pCurrentView(pCurrentView), m_isBuilt(false)
    {
    }

    virtual const std::vector<OwnerPtr<IMenuItemDefinition>>& GetMenuItems() const override
    {
        if (!m_isBuilt)
        {
            BuildMenuItems();
            m_isBuilt = true;
        }

        return m_menuItems;
    }

private:
    mutable bool m_isBuilt;
    mutable std::vector<OwnerPtr<IMenuItemDefinition>> m_menuItems;
    const IElement* m_pElement;
    const IUiView* m_pCurrentView;

    void BuildMenuItems() const
    {
        // 实际构建菜单项
        DynamicMenuGenerator::GenerateElementSpecificMenu(m_pElement,
            const_cast<LazyMenuItemContainer*>(this));
    }
};
```

## 菜单调试与测试

### 1. 菜单调试工具

```cpp
class MenuDebugger
{
public:
    static void DumpMenuStructure(const IMenuItemContainerDefinition* pMenu)
    {
        if (!pMenu)
            return;

        DBG_INFO(L"=== Menu Structure Dump ===");
        DumpMenuLevel(pMenu, 0);
        DBG_INFO(L"=== End Menu Dump ===");
    }

    static void LogMenuCreation(
        const std::wstring& menuId,
        const std::wstring& elementType,
        const std::wstring& selectionState)
    {
        DBG_INFO(L"Menu Created: ID=%s, ElementType=%s, Selection=%s",
                 menuId.c_str(), elementType.c_str(), selectionState.c_str());
    }

private:
    static void DumpMenuLevel(const IMenuItemContainerDefinition* pMenu, int level)
    {
        std::wstring indent(level * 2, L' ');

        const std::vector<OwnerPtr<IMenuItemDefinition>>& items = pMenu->GetMenuItems();

        for (const auto& item : items)
        {
            if (const ICommandMenuItemDefinition* pCmdItem =
                quick_cast<ICommandMenuItemDefinition>(item.get()))
            {
                DBG_INFO(L"%s[CMD] %s -> %s",
                         indent.c_str(),
                         pCmdItem->GetCaption().c_str(),
                         pCmdItem->GetCommandId().c_str());
            }
            else if (const IMenuItemContainerDefinition* pSubMenu =
                     quick_cast<IMenuItemContainerDefinition>(item.get()))
            {
                DBG_INFO(L"%s[SUBMENU] %s",
                         indent.c_str(),
                         pSubMenu->GetCaption().c_str());
                DumpMenuLevel(pSubMenu, level + 1);
            }
            else
            {
                DBG_INFO(L"%s[SEPARATOR]", indent.c_str());
            }
        }
    }
};
```

### 2. 菜单单元测试

```cpp
class MenuUnitTests
{
public:
    static bool RunAllTests()
    {
        bool allPassed = true;

        allPassed &= TestBasicMenuCreation();
        allPassed &= TestElementSpecificMenus();
        allPassed &= TestConditionalMenus();
        allPassed &= TestUiAdapterIntegration();
        allPassed &= TestMenuCaching();

        return allPassed;
    }

private:
    static bool TestBasicMenuCreation()
    {
        // 测试基础菜单创建
        OwnerPtr<ICommandMenuItemDefinition> pMenuItem =
            ControlDefinitionFactory::CreateCommandMenuItem(
                L"test_menu", L"TEST_CMD", L"Test Menu", L"Test Tooltip", L"test.png");

        return pMenuItem &&
               pMenuItem->GetId() == L"test_menu" &&
               pMenuItem->GetCommandId() == L"TEST_CMD" &&
               pMenuItem->GetCaption() == L"Test Menu";
    }

    static bool TestElementSpecificMenus()
    {
        // 创建测试墙图元
        MockWall mockWall;

        // 测试墙特定菜单生成
        OwnerPtr<IMenuItemContainerDefinition> pMenu =
            ControlDefinitionFactory::CreateMenuItemContainer(L"test", L"");

        DynamicMenuGenerator::GenerateElementSpecificMenu(&mockWall, pMenu.get());

        // 验证菜单项数量和内容
        const auto& items = pMenu->GetMenuItems();
        return items.size() > 0; // 应该有墙特定的菜单项
    }

    static bool TestConditionalMenus()
    {
        // 测试条件菜单显示逻辑
        MockWall shortWall(500.0); // 短墙
        MockWall longWall(2000.0); // 长墙

        OwnerPtr<ICommandMenuItemDefinition> pSplitMenu =
            ControlDefinitionFactory::CreateCommandMenuItem(
                L"wall_split", L"CMD_SPLIT_WALL", L"Split Wall", L"", L"");

        // 短墙不应该显示分割菜单
        bool shortWallResult = !ConditionalMenuManager::ShouldShowMenuItem(
            pSplitMenu.get(), &shortWall, nullptr);

        // 长墙应该显示分割菜单
        bool longWallResult = ConditionalMenuManager::ShouldShowMenuItem(
            pSplitMenu.get(), &longWall, nullptr);

        return shortWallResult && longWallResult;
    }

    static bool TestUiAdapterIntegration()
    {
        // 测试UiAdapter策略集成
        OwnerPtr<TestContextMenuStrategy> pStrategy =
            NEW_AS_OWNER_PTR(TestContextMenuStrategy);

        UiAdapter::RegisterContextMenuStrategy(TransferOwnership(pStrategy));

        // 创建测试环境并验证策略是否被调用
        MockDocument mockDoc;
        MockElement mockElement;
        OwnerPtr<IMenuItemContainerDefinition> pMenu =
            ControlDefinitionFactory::CreateMenuItemContainer(L"test", L"");

        UiAdapter::ApplyContextMenuStrategy(&mockDoc, mockElement.GetElementId(), pMenu.get());

        // 验证菜单是否被修改
        return pMenu->GetMenuItems().size() > 0;
    }

    static bool TestMenuCaching()
    {
        // 测试菜单缓存机制
        std::wstring cacheKey = L"test_cache_key";

        OwnerPtr<IMenuItemContainerDefinition> pOriginalMenu =
            ControlDefinitionFactory::CreateMenuItemContainer(L"test", L"Test Menu");

        // 缓存菜单
        MenuCacheManager::CacheMenu(cacheKey, CloneMenuContainer(pOriginalMenu.get()));

        // 从缓存获取菜单
        OwnerPtr<IMenuItemContainerDefinition> pCachedMenu =
            MenuCacheManager::GetCachedMenu(cacheKey);

        return pCachedMenu && pCachedMenu->GetCaption() == L"Test Menu";
    }
};
```

## 最佳实践与建议

### 1. 菜单设计原则

- **用户体验优先**: 菜单结构应该直观易懂，常用功能放在显眼位置
- **上下文相关**: 菜单内容应该与当前选择的图元类型和状态相关
- **性能考虑**: 避免在菜单构建时执行耗时操作，使用延迟加载
- **可扩展性**: 设计时考虑未来功能扩展的需要
- **一致性**: 保持菜单风格和交互方式的一致性

### 2. 配置管理建议

```cpp
class MenuConfigurationBestPractices
{
public:
    // 配置文件版本管理
    static bool ValidateConfigVersion(const std::wstring& configPath)
    {
        XMLDocument doc;
        if (doc.LoadFile(WStringToString(configPath).c_str()) != XML_SUCCESS)
            return false;

        XMLElement* pRoot = doc.FirstChildElement("CustomizeShortcutMenu");
        if (!pRoot)
            return false;

        const char* version = pRoot->Attribute("version");
        if (!version)
            return false;

        return ValidateVersion(StringToWString(version));
    }

    // 配置文件备份
    static void BackupConfiguration(const std::wstring& configPath)
    {
        std::wstring backupPath = configPath + L".backup." + GetCurrentTimestamp();
        FileSystem::CopyFile(configPath, backupPath);
    }

    // 配置文件合并
    static bool MergeConfigurations(
        const std::wstring& baseConfigPath,
        const std::wstring& customConfigPath,
        const std::wstring& outputPath)
    {
        // 实现配置文件合并逻辑
        return true;
    }

private:
    static bool ValidateVersion(const std::wstring& version);
    static std::wstring GetCurrentTimestamp();
};
```

### 3. 错误处理与容错

```cpp
class MenuErrorHandler
{
public:
    static OwnerPtr<IMenuItemContainerDefinition> CreateSafeMenu(
        const IElement* pElement,
        const IUiView* pCurrentView)
    {
        try
        {
            // 尝试正常创建菜单
            return CreateNormalMenu(pElement, pCurrentView);
        }
        catch (const std::exception& e)
        {
            // 创建失败时返回基础菜单
            DBG_WARN(L"菜单创建失败，使用基础菜单: %s",
                     StringToWString(e.what()).c_str());
            return CreateFallbackMenu();
        }
    }

private:
    static OwnerPtr<IMenuItemContainerDefinition> CreateNormalMenu(
        const IElement* pElement,
        const IUiView* pCurrentView)
    {
        // 正常菜单创建逻辑
        return nullptr;
    }

    static OwnerPtr<IMenuItemContainerDefinition> CreateFallbackMenu()
    {
        // 创建基础的后备菜单
        OwnerPtr<IMenuItemContainerDefinition> pMenu =
            ControlDefinitionFactory::CreateMenuItemContainer(L"fallback", L"");

        // 添加基础菜单项
        OwnerPtr<ICommandMenuItemDefinition> pProperties =
            ControlDefinitionFactory::CreateCommandMenuItem(
                L"properties", L"CMD_PROPERTIES", L"属性", L"查看属性", L"");
        pMenu->AddMenuItem(TransferOwnership(pProperties));

        return pMenu;
    }
};
```

## 总结

上下文菜单定制系统是 ActionModify 中的重要组成部分，通过以下几个层次实现了高度的可扩展性：

1. **配置文件系统**: 通过 XML 配置文件定义基础菜单结构
2. **工厂模式**: 使用 ControlDefinitionFactory 创建各种菜单项
3. **策略模式**: 通过 UiAdapter 和 IElementContextMenuStrategy 实现动态扩展
4. **条件显示**: 根据图元状态、用户权限等条件动态显示菜单项
5. **性能优化**: 通过缓存、延迟加载等机制提升性能

这种设计使得开发者可以在不修改核心代码的情况下，通过配置文件和扩展策略灵活定制上下文菜单，满足不同业务场景的需求。