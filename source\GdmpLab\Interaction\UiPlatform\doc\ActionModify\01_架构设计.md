# ActionModify 架构设计

## 概述

ActionModify 是 GDMPLab 中负责图元修改操作的核心交互组件，采用了多层架构设计，通过继承、组合和策略模式实现了高度的可扩展性和可定制性。

## 类继承关系

```mermaid
classDiagram
    class ActionBase {
        +InitAction()
        +OnLButtonDown()
        +OnLButtonUp()
        +OnMovePoint()
        +OnKeyDown()
        +GetPromptMessage()
    }
    
    class GbmpPickActionBase {
        +UpdateCandidates()
        +OnSelectionChanged()
        +AddToSelection()
        +ClearSelection()
        -m_status
        -m_pickTarget
        -m_upPickFilter
    }
    
    class GbmpActionModify {
        +OnRButtonDown()
        +OnRButtonUp()
        +OnLButtonDoubleClick()
        +PrepareContextMenu()
        +SetModifyElementsBehavior()
        -m_opModifyElementsBehavior
        -m_oSnapContext
    }
    
    ActionBase <|-- GbmpPickActionBase
    GbmpPickActionBase <|-- GbmpActionModify
```

## 核心组件关系

```mermaid
classDiagram
    class GbmpActionModify {
        -m_opModifyElementsBehavior
        -m_opPickEventHandlers
        -m_oFilterForLocalSnap
        -m_oFilterForRemoteSnap
        -m_oSnapContext
    }
    
    class IActionModifyBehavior {
        <<interface>>
        +CreateElementShadow()
        +ModifyElement()
        +ModifyGripPoint()
        +PrepareContextMenu()
        +OnLButtonDoubleClick()
    }
    
    class GbmpModifyElementsBehavior {
        +CreateElementShadow()
        +ModifyElement()
        +ModifyGripPoint()
        +PrepareContextMenu()
    }
    
    class IPickEventHandler {
        <<interface>>
        +HandlePickEvent()
    }
    
    class IPickFilter {
        <<interface>>
        +IsElementAllowed()
    }
    
    class ISnapContext {
        <<interface>>
        +GetSnapCandidates()
        +SnapPoint()
    }
    
    GbmpActionModify --> IActionModifyBehavior
    GbmpActionModify --> IPickEventHandler
    GbmpActionModify --> IPickFilter
    GbmpActionModify --> ISnapContext
    IActionModifyBehavior <|.. GbmpModifyElementsBehavior
```

## 设计模式应用

### 1. 策略模式 (Strategy Pattern)

**应用场景**: 修改行为的定制化

```cpp
// IActionModifyBehavior 作为策略接口
class IActionModifyBehavior {
public:
    virtual bool ModifyElement(const IUiView* pCurrentView, 
                              const ElementId& modifiedElementId, 
                              const Vector3d& originPt, 
                              const Vector3d& moveVector) = 0;
    virtual bool ModifyGripPoint(IUiView* pCurrentView, 
                                const ElementId& modifiedElementId, 
                                const Vector3d& originPt, 
                                const Vector3d& moveVector) = 0;
};

// GbmpActionModify 作为上下文
class GbmpActionModify {
private:
    OwnerPtr<IActionModifyBehavior> m_opModifyElementsBehavior;
public:
    void SetModifyElementsBehavior(OwnerPtr<IActionModifyBehavior> opModifyBehavior);
};
```

### 2. 责任链模式 (Chain of Responsibility)

**应用场景**: 事件处理器链

```cpp
class GbmpActionModify {
private:
    std::vector<OwnerPtr<IPickEventHandler>> m_opPickEventHandlers;
    std::vector<OwnerPtr<IPickEventHandler>> m_opRectPickEventHandlers;
    
public:
    bool AddPickPostProcessEventHandler(OwnerPtr<IPickEventHandler> opEventHandler);
    bool AddRectPickPostProcessEventHandler(OwnerPtr<IPickEventHandler> opEventHandler);
};
```

### 3. 观察者模式 (Observer Pattern)

**应用场景**: 选择集变化通知

```cpp
class GbmpPickActionBase {
protected:
    virtual void OnSelectionChanged();  // 通知选择集变化
};

class GbmpActionModify {
protected:
    virtual void OnSelectionChanged() override;  // 响应选择集变化
};
```

## 核心接口设计

### IActionModifyBehavior 接口

该接口定义了修改操作的核心行为，支持以下功能：

1. **影子对象创建**: `CreateElementShadow()` - 拖拽时显示的临时对象
2. **图元修改**: `ModifyElement()` - 实际的图元修改逻辑
3. **夹点修改**: `ModifyGripPoint()` - 夹点拖拽的处理逻辑
4. **后处理**: `ProcessPostModify()` - 修改完成后的处理
5. **上下文菜单**: `PrepareContextMenu()` - 右键菜单定制
6. **双击处理**: `OnLButtonDoubleClick()` - 双击事件处理

### 关键成员变量

```cpp
class GbmpActionModify {
private:
    // 核心行为定制
    OwnerPtr<IActionModifyBehavior> m_opModifyElementsBehavior;
    
    // 事件处理器
    std::vector<OwnerPtr<IPickEventHandler>> m_opPickEventHandlers;
    std::vector<OwnerPtr<IPickEventHandler>> m_opRectPickEventHandlers;
    
    // 捕捉相关
    OwnerPtr<ISnapContext> m_oSnapContext;
    OwnerPtr<IPickFilter> m_oFilterForLocalSnap;    // 近程捕捉过滤器
    OwnerPtr<IPickFilter> m_oFilterForRemoteSnap;   // 远程捕捉过滤器
    OwnerPtr<ISnapCandidates> m_oSnapCandidates;
    
    // 移动相关
    std::vector<ElementId> m_moveElementIds;        // 待移动的图元ID列表
    bool m_isCaught;                                // 捕获标记
    Vector3d m_nearStartPt;                         // 最近起点
    Vector3d m_interPt;                             // 交点
    Vector3d m_endPt;                               // 终点
    
    // 影子对象管理
    typedef std::map<ElementId, ElementId> ElementToShadowMap;
    ElementToShadowMap m_elementShadows;            // 图元到影子对象的映射
};
```

## 扩展机制

### 1. Behavior 扩展

通过实现 `IActionModifyBehavior` 接口，可以定制修改操作的具体行为：

```cpp
class CustomModifyBehavior : public IActionModifyBehavior {
public:
    virtual bool ModifyElement(const IUiView* pCurrentView, 
                              const ElementId& modifiedElementId, 
                              const Vector3d& originPt, 
                              const Vector3d& moveVector) override {
        // 自定义修改逻辑
        return true;
    }
};
```

### 2. EventHandler 扩展

通过添加事件处理器，可以在拾取过程中插入自定义逻辑：

```cpp
class CustomPickEventHandler : public IPickEventHandler {
public:
    virtual bool HandlePickEvent(const IPickEvent* pPickEvent) override {
        // 自定义拾取处理逻辑
        return true;
    }
};
```

### 3. Filter 扩展

通过自定义过滤器，可以控制哪些图元可以被拾取：

```cpp
class CustomPickFilter : public IPickFilter {
public:
    virtual bool IsElementAllowed(const IElement* pElement) override {
        // 自定义过滤逻辑
        return true;
    }
};
```

## 架构优势

1. **高度可扩展**: 通过接口和策略模式，支持灵活的功能扩展
2. **职责分离**: 不同功能模块职责清晰，便于维护
3. **可配置性**: 通过组合不同的组件，可以配置出不同的行为
4. **复用性**: 基础功能可以在不同场景下复用

## 设计考虑

1. **性能优化**: 大场景下的拾取和选择性能
2. **内存管理**: 使用 OwnerPtr 进行自动内存管理
3. **线程安全**: 考虑多线程环境下的安全性
4. **向后兼容**: 保持与旧版本的兼容性
