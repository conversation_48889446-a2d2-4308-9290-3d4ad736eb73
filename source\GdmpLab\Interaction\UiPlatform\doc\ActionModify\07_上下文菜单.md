# ActionModify 上下文菜单

## 概述

上下文菜单（右键菜单）是 ActionModify 中重要的用户交互机制。它根据当前选择的图元类型和操作状态，动态生成相应的菜单项，为用户提供快捷的操作入口。上下文菜单支持自定义配置和扩展。

## 核心接口架构

### 1. IMenuItemContainerDefinition 菜单容器

菜单容器用于组织和管理菜单项：

```cpp
class IMenuItemContainerDefinition : public IObject
{
public:
    // 创建菜单容器
    static OwnerPtr<IMenuItemContainerDefinition> Create(
        const std::wstring& id, 
        const std::wstring& text
    );
    
    // 菜单项管理
    virtual bool AddSubMenuItem(OwnerPtr<IMenuItemDefinition> pMenuItem) = 0;
    virtual bool AddSeparator() = 0;
    virtual bool RemoveSubMenuItem(const std::wstring& id) = 0;
    
    // 属性访问
    virtual std::wstring GetId() const = 0;
    virtual std::wstring GetText() const = 0;
    virtual std::vector<const IMenuItemDefinition*> GetSubMenuItems() const = 0;
};
```

### 2. IMenuItemDefinition 菜单项定义

单个菜单项的定义：

```cpp
class IMenuItemDefinition : public IObject
{
public:
    // 基础属性
    virtual std::wstring GetId() const = 0;
    virtual std::wstring GetText() const = 0;
    virtual std::wstring GetTooltip() const = 0;
    virtual std::wstring GetIcon() const = 0;
    virtual ControlDefinitionType GetType() const = 0;
    
    // 状态属性
    virtual bool IsEnabled() const = 0;
    virtual bool IsVisible() const = 0;
    virtual bool IsChecked() const = 0;
};
```

### 3. ICommandMenuItemDefinition 命令菜单项

执行特定命令的菜单项：

```cpp
class ICommandMenuItemDefinition : public IMenuItemDefinition
{
public:
    // 创建命令菜单项
    static OwnerPtr<ICommandMenuItemDefinition> Create(
        const std::wstring& id,
        const std::wstring& commandId,
        const std::wstring& text,
        const std::wstring& tooltip,
        const std::wstring& icon
    );
    
    // 命令属性
    virtual std::wstring GetCommandId() const = 0;
    virtual void SetCommandId(const std::wstring& commandId) = 0;
};
```

### 4. ICustomizeShortcutMenuManager 菜单管理器

管理自定义右键菜单配置：

```cpp
class ICustomizeShortcutMenuManager
{
public:
    // 获取全局管理器实例
    static ICustomizeShortcutMenuManager* Get();
    
    // 配置管理
    virtual bool ReadCustomizeShortcutMenu(const std::wstring& configurationFolderName) = 0;
    
    // 菜单数据获取
    virtual std::vector<OwnerPtr<IMenuItemDefinition>> GetShortcutMenuData(
        const IElement* pElement) = 0;
    virtual std::vector<OwnerPtr<IMenuItemDefinition>> GetShortcutMenuNoSelectData() = 0;
    virtual std::vector<OwnerPtr<IMenuItemDefinition>> GetDefaultShortcutMenuData() = 0;
    
    // 菜单项查找
    virtual const IMenuItemDefinition* FindMenuItem(
        const std::wstring& menuId, 
        const IElement* pElement) = 0;
};
```

## 上下文菜单架构图

```mermaid
classDiagram
    class GbmpActionModify {
        +PrepareContextMenu(view) OwnerPtr~IMenuItemContainerDefinition~
        +OnRButtonDown(view, pos) bool
        +OnRButtonUp(view, pos) bool
    }
    
    class IActionModifyBehavior {
        <<interface>>
        +PrepareContextMenu(view) OwnerPtr~IMenuItemContainerDefinition~
    }
    
    class GbmpModifyElementsBehavior {
        +PrepareContextMenu(view) OwnerPtr~IMenuItemContainerDefinition~
    }
    
    class IMenuItemContainerDefinition {
        +Create(id, text) OwnerPtr~IMenuItemContainerDefinition~
        +AddSubMenuItem(item) bool
        +AddSeparator() bool
        +GetSubMenuItems() vector~IMenuItemDefinition*~
    }
    
    class IMenuItemDefinition {
        <<interface>>
        +GetId() wstring
        +GetText() wstring
        +GetType() ControlDefinitionType
        +IsEnabled() bool
    }
    
    class ICommandMenuItemDefinition {
        +Create(id, cmdId, text, tooltip, icon) OwnerPtr~ICommandMenuItemDefinition~
        +GetCommandId() wstring
    }
    
    class ICustomizeShortcutMenuManager {
        +Get() ICustomizeShortcutMenuManager*
        +ReadCustomizeShortcutMenu(folder) bool
        +GetShortcutMenuData(element) vector~IMenuItemDefinition~
        +GetShortcutMenuNoSelectData() vector~IMenuItemDefinition~
        +GetDefaultShortcutMenuData() vector~IMenuItemDefinition~
    }
    
    GbmpActionModify --> IActionModifyBehavior : delegates to
    IActionModifyBehavior <|-- GbmpModifyElementsBehavior
    GbmpModifyElementsBehavior --> ICustomizeShortcutMenuManager : uses
    GbmpModifyElementsBehavior --> IMenuItemContainerDefinition : creates
    IMenuItemContainerDefinition --> IMenuItemDefinition : contains
    IMenuItemDefinition <|-- ICommandMenuItemDefinition
```

## 上下文菜单生成流程

### 1. 菜单触发入口

```cpp
OwnerPtr<IMenuItemContainerDefinition> GbmpActionModify::PrepareContextMenu(IUiView* pUIView)
{
    // 检查子操作结束原因
    ActionFinishReason childActionCancelReason = GetChildFinishReson();
    if (childActionCancelReason == ActionFinishReason::RButtonDown ||
        childActionCancelReason == ActionFinishReason::RButtonUp)
    {
        // 子操作如果是右键结束，不需要弹右键菜单
        KillMe();
        return nullptr;
    }
    
    // 委托给修改行为处理
    OwnerPtr<IMenuItemContainerDefinition> opContextMenu;
    if (m_opModifyElementsBehavior)
    {
        opContextMenu = m_opModifyElementsBehavior->PrepareContextMenu(pUIView);
    }
    
    UpdateView();
    return opContextMenu;
}
```

### 2. 右键事件处理

```cpp
bool GbmpActionModify::OnRButtonDown(IUiView* pCurrentView, const Vector3d& pos)
{
    // 右键按下事件处理
    return true;
}

bool GbmpActionModify::OnRButtonUp(IUiView* pCurrentView, const Vector3d& pos)
{
    // 检查子操作结束原因
    ActionFinishReason childActionCancelReason = GetChildFinishReson();
    if (childActionCancelReason == ActionFinishReason::RButtonDown ||
        childActionCancelReason == ActionFinishReason::RButtonUp)
    {
        // 子操作右键结束，不处理
        return false;
    }
    
    // 其他右键处理逻辑
    return true;
}
```

### 3. 修改行为菜单生成

```cpp
OwnerPtr<IMenuItemContainerDefinition> GbmpModifyElementsBehavior::PrepareContextMenu(IUiView* pCurrentView)
{
    IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();
    
    // 获取拾取候选
    const GraphicsNodeReferenceOwnerPtrSet& pickCandidate = GetPickCandidates();
    
    // 创建菜单容器
    const std::wstring modifyActionMenuId = L"_ModifyActionMenu";
    OwnerPtr<IMenuItemContainerDefinition> pMenuItemContainer = 
        IMenuItemContainerDefinition::Create(modifyActionMenuId, L"");
    
    // 菜单项添加辅助函数
    auto AddSubMenuFun = [&pMenuItemContainer](std::vector<OwnerPtr<IMenuItemDefinition>>& menus) {
        for (auto& iter : menus)
        {
            if (iter->GetType() == ControlDefinitionType::Separator) {
                pMenuItemContainer->AddSeparator();
            }
            else {
                pMenuItemContainer->AddSubMenuItem(TransferOwnership(iter));
            }
        }
    };
    
    // 读取自定义菜单配置
    ICustomizeShortcutMenuManager::Get()->ReadCustomizeShortcutMenu(L"shortcutMenu_config");
    
    if (1 == pickCandidate.size())
    {
        // 单个图元选中时的菜单
        IElement* pElement = pDoc->GetElement(pickCandidate.at(0)->GetElementId());
        std::vector<OwnerPtr<IMenuItemDefinition>> menus = 
            ICustomizeShortcutMenuManager::Get()->GetShortcutMenuData(pElement);
        AddSubMenuFun(menus);
        
        // 应用上下文菜单策略（如果启用UiAdapter）
        #ifdef UiAdapter_IMPL
        Sample::UiAdapter::ApplyContextMenuStrategy(
            pDoc, 
            pickCandidate.at(0)->GetElementId(), 
            pMenuItemContainer.get()
        );
        #endif
    }
    else if (ISelection::Get()->IsEmpty())
    {
        // 没有选中对象时的菜单
        std::vector<OwnerPtr<IMenuItemDefinition>> menus = 
            ICustomizeShortcutMenuManager::Get()->GetShortcutMenuNoSelectData();
        AddSubMenuFun(menus);
    }
    else
    {
        // 多选时的默认菜单
        std::vector<OwnerPtr<IMenuItemDefinition>> menus = 
            ICustomizeShortcutMenuManager::Get()->GetDefaultShortcutMenuData();
        AddSubMenuFun(menus);
        
        // 在位编辑菜单项（单选时）
        if (ISelection::Get()->GetCount() == 1)
        {
            std::wstring strCmdId = ID_CMD_ENTER_INPLACE_EDIT_MODE;
            // ... 在位编辑菜单项创建逻辑
        }
        
        // 表格子菜单
        if (IsNeedShowTableSubMenu(pCurrentView))
        {
            #ifndef UiAdapter_IMPL
            AddDrawingTableMenuItem(pMenuItemContainer.get());
            #else
            if (!pickCandidate.empty()) {
                Sample::UiAdapter::ApplyContextMenuStrategy(
                    pDoc, 
                    pickCandidate.at(0)->GetElementId(), 
                    pMenuItemContainer.get()
                );
            }
            #endif
        }
    }
    
    return pMenuItemContainer;
}
```

## 菜单配置系统

### 1. 配置文件读取

```cpp
// 读取自定义右键菜单配置
bool success = ICustomizeShortcutMenuManager::Get()->ReadCustomizeShortcutMenu(L"shortcutMenu_config");

// 配置文件路径：{配置文件夹}/CustomizeShortcutMenu.xml
```

### 2. 图元特定菜单

```cpp
// 根据图元类型获取菜单
IElement* pElement = pDoc->GetElement(elementId);
std::vector<OwnerPtr<IMenuItemDefinition>> menus = 
    ICustomizeShortcutMenuManager::Get()->GetShortcutMenuData(pElement);

// 菜单项会根据图元的类型、属性等动态生成
```

### 3. 默认菜单类型

```cpp
// 无选择时的菜单
std::vector<OwnerPtr<IMenuItemDefinition>> noSelectMenus = 
    ICustomizeShortcutMenuManager::Get()->GetShortcutMenuNoSelectData();

// 多选时的默认菜单
std::vector<OwnerPtr<IMenuItemDefinition>> defaultMenus = 
    ICustomizeShortcutMenuManager::Get()->GetDefaultShortcutMenuData();
```

## 特殊菜单处理

### 1. 表格菜单

```cpp
bool IsNeedShowTableSubMenu(IUiView* pCurrentView)
{
    // 检查当前是否需要显示表格子菜单
    // 根据选中的图元类型判断
    return false; // 具体实现逻辑
}

void AddDrawingTableMenuItem(IMenuItemContainerDefinition* pMenuContainer)
{
    // 添加表格相关的菜单项
    // 如：插入行、插入列、删除行、删除列等
}
```

### 2. 在位编辑菜单

```cpp
// 在位编辑菜单项
if (ISelection::Get()->GetCount() == 1)
{
    std::wstring strCmdId = ID_CMD_ENTER_INPLACE_EDIT_MODE;
    
    // 检查是否支持在位编辑
    const GraphicsNodeReferenceOwnerPtrSet& selections = ISelection::Get()->GetGraphicsNodeReferences();
    if (!selections.empty())
    {
        ElementId elementId = (*selections.begin())->GetElementId();
        IElement* pElement = pDoc->GetElement(elementId);
        
        // 根据图元类型判断是否支持在位编辑
        if (SupportsInPlaceEdit(pElement))
        {
            OwnerPtr<ICommandMenuItemDefinition> pDefMenuItem = 
                ICommandMenuItemDefinition::Create(
                    strCmdId, 
                    strCmdId, 
                    GBMP_TR(L"在位编辑构件"), 
                    L"", 
                    L""
                );
            pMenuItemContainer->AddSubMenuItem(TransferOwnership(pDefMenuItem));
        }
    }
}
```

### 3. 分隔符处理

```cpp
// 添加菜单项时处理分隔符
auto AddSubMenuFun = [&pMenuItemContainer](std::vector<OwnerPtr<IMenuItemDefinition>>& menus) {
    for (auto& iter : menus)
    {
        if (iter->GetType() == ControlDefinitionType::Separator) {
            // 添加分隔符
            pMenuItemContainer->AddSeparator();
        }
        else {
            // 添加普通菜单项
            pMenuItemContainer->AddSubMenuItem(TransferOwnership(iter));
        }
    }
};
```

## 菜单扩展机制

### 1. UiAdapter 扩展

```cpp
#ifdef UiAdapter_IMPL
// 应用上下文菜单策略
Sample::UiAdapter::ApplyContextMenuStrategy(
    pDoc, 
    elementId, 
    pMenuItemContainer.get()
);
#endif
```

### 2. 自定义菜单项创建

```cpp
// 创建命令菜单项
OwnerPtr<ICommandMenuItemDefinition> CreateCommandMenuItem(
    const std::wstring& id,
    const std::wstring& commandId,
    const std::wstring& text,
    const std::wstring& tooltip = L"",
    const std::wstring& icon = L""
)
{
    return ICommandMenuItemDefinition::Create(id, commandId, text, tooltip, icon);
}

// 创建分隔符
OwnerPtr<IMenuItemDefinition> CreateSeparator()
{
    // 返回类型为 ControlDefinitionType::Separator 的菜单项
    return nullptr; // 具体实现
}
```

### 3. 动态菜单生成

```cpp
// 根据图元状态动态生成菜单
std::vector<OwnerPtr<IMenuItemDefinition>> GenerateDynamicMenu(const IElement* pElement)
{
    std::vector<OwnerPtr<IMenuItemDefinition>> menus;
    
    // 根据图元类型添加不同菜单项
    if (const IWall* pWall = quick_cast<IWall>(pElement))
    {
        // 墙相关菜单
        menus.push_back(CreateCommandMenuItem(
            L"EditWallProfile", 
            L"CMD_EDIT_WALL_PROFILE", 
            L"编辑轮廓"
        ));
    }
    else if (const IBeam* pBeam = quick_cast<IBeam>(pElement))
    {
        // 梁相关菜单
        menus.push_back(CreateCommandMenuItem(
            L"EditBeamProfile", 
            L"CMD_EDIT_BEAM_PROFILE", 
            L"编辑截面"
        ));
    }
    
    return menus;
}
```

## 菜单事件处理

### 1. 菜单项状态管理

```cpp
// 菜单项启用/禁用状态
virtual bool IsEnabled() const = 0;

// 菜单项可见性
virtual bool IsVisible() const = 0;

// 菜单项选中状态
virtual bool IsChecked() const = 0;
```

### 2. 命令执行

```cpp
// 菜单项点击时执行对应命令
std::wstring commandId = pMenuItem->GetCommandId();

// 通过命令系统执行命令
ICommandManager::Get()->ExecuteCommand(commandId, parameters);
```

## 调试支持

### 1. 菜单调试信息

```cpp
// 输出菜单结构信息
void DebugPrintMenuStructure(const IMenuItemContainerDefinition* pContainer)
{
    std::vector<const IMenuItemDefinition*> items = pContainer->GetSubMenuItems();
    for (const auto* item : items)
    {
        if (item->GetType() == ControlDefinitionType::Separator)
        {
            DBG_INFO(L"Separator");
        }
        else
        {
            DBG_INFO(L"MenuItem: %s (ID: %s)", item->GetText().c_str(), item->GetId().c_str());
        }
    }
}
```

### 2. 菜单配置验证

```cpp
// 验证菜单配置是否正确加载
bool ValidateMenuConfiguration()
{
    ICustomizeShortcutMenuManager* pManager = ICustomizeShortcutMenuManager::Get();
    
    // 检查默认菜单
    std::vector<OwnerPtr<IMenuItemDefinition>> defaultMenus = 
        pManager->GetDefaultShortcutMenuData();
    
    if (defaultMenus.empty())
    {
        DBG_WARN(L"Default menu configuration is empty");
        return false;
    }
    
    return true;
}
```
