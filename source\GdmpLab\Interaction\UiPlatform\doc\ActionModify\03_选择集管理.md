# ActionModify 选择集管理

## 概述

选择集管理是 ActionModify 的核心功能之一，负责维护当前选中的图元集合，处理选择集的增加、删除、清空等操作，并管理选择集变化时的通知机制。

## 选择集核心接口

### ISelection 单例

GDMP 提供的全局选择集管理器：

```cpp
class ISelection
{
public:
    static ISelection* Get();  // 获取单例实例
    
    // 选择集操作
    void Clear(IDocument* pDoc);                    // 清空选择集
    void Add(IDocument* pDoc, const ElementId& id); // 添加图元到选择集
    void Remove(IDocument* pDoc, const ElementId& id); // 从选择集移除图元
    
    // 选择集查询
    int GetCount() const;                           // 获取选择集数量
    bool Contains(const ElementId& id) const;       // 检查是否包含指定图元
    std::vector<ElementId> GetAllElementIds() const; // 获取所有选中的图元ID
};
```

### IHighlights 高亮管理

管理选中图元的高亮显示：

```cpp
class IHighlights
{
public:
    static IHighlights* Get();  // 获取单例实例
    
    void Clear();  // 清空高亮
    void AddGraphicsNodeReferences(const GraphicsNodeReferenceOwnerPtrVector& refs);
    void SetGraphicsNodeReferences(const GraphicsNodeReferenceOwnerPtrVector& refs);
};
```

## 选择集操作方法

### 1. 添加到选择集

```cpp
void GbmpPickActionBase::AddToSelection(IDocument* pDocument, 
                                       const IGraphicsNodeReference& pickResult, 
                                       const IUiView* pCurrentView)
{
    // 检查是否可以被选择
    if (!IsCanBeSelect(pDocument, pickResult))
        return;
        
    // 检查编辑模式权限
    IEditMode* pEditModeUE = IEditMode::GetTopActiveEditMode(pDocument);
    if (pEditModeUE)
    {
        const IElement* pElement = pDocument->GetElement(pickResult.GetElementId());
        if (!pEditModeUE->IsElementEditable(pElement))
            return;
    }
    
    // 添加到选择集
    ISelection::Get()->Add(pDocument, pickResult.GetElementId());
    
    // 更新高亮
    GraphicsNodeReferenceOwnerPtrVector highlights;
    highlights.push_back(pickResult.Clone());
    IHighlights::Get()->AddGraphicsNodeReferences(highlights);
}
```

### 2. 批量添加到选择集

```cpp
void GbmpPickActionBase::AddToSelectionGroup(IDocument* pDoc, 
                                            GraphicsNodeReferenceOwnerPtrSet& pickResults)
{
    if (pickResults.empty())
        return;
        
    // 检查是否有表格在选择集中
    bool hasTable = false;
    HasTableInSelection(pDoc, pickResults, hasTable);
    
    // 处理表格选择的特殊逻辑
    if (hasTable)
    {
        // 表格相关的特殊处理
        // ...
    }
    
    // 批量添加到选择集
    for (auto& pickResult : pickResults)
    {
        if (IsCanBeSelect(pDoc, *pickResult))
        {
            ISelection::Get()->Add(pDoc, pickResult->GetElementId());
        }
    }
    
    // 更新高亮
    GraphicsNodeReferenceOwnerPtrVector highlights;
    for (auto& pickResult : pickResults)
    {
        highlights.push_back(pickResult->Clone());
    }
    IHighlights::Get()->AddGraphicsNodeReferences(highlights);
}
```

### 3. 清空选择集

```cpp
void GbmpPickActionBase::ClearSelection(IDocument* pDoc)
{
    ISelection::Get()->Clear(pDoc);
    IHighlights::Get()->Clear();
}

bool GbmpPickActionBase::IsSelectionEmpty()
{
    return ISelection::Get()->GetCount() <= 0;
}
```

## 选择集变化通知

### 1. 选择集变化回调

```cpp
class GbmpPickActionBase
{
protected:
    // 选择集变化时的回调方法，子类可以重写
    virtual void OnSelectionChanged();
};

class GbmpActionModify : public GbmpPickActionBase
{
protected:
    virtual void OnSelectionChanged() override;
};
```

### 2. 选择集变化处理流程

```mermaid
sequenceDiagram
    participant User as 用户操作
    participant Action as GbmpActionModify
    participant Base as GbmpPickActionBase
    participant Selection as ISelection
    participant Highlights as IHighlights
    participant Behavior as IActionModifyBehavior
    
    User->>Action: 点击选择图元
    Action->>Base: AddToSelection()
    Base->>Selection: Add(elementId)
    Base->>Highlights: AddGraphicsNodeReferences()
    Base->>Base: OnSelectionChanged()
    Base->>Action: OnSelectionChanged()
    Action->>Behavior: OnSelectionChanged()
    Behavior->>Behavior: 刷新属性面板等
```

## 多选逻辑

### 1. 键盘修饰键处理

```cpp
bool GbmpPickActionBase::OnLButtonDown(IUiView* pCurrentView, const Vector3d& pos)
{
    // Ctrl 键：累加选择
    bool isCtrlPressed = IsKeyAndButtonPressed(VK_CONTROL);
    
    // Shift 键：范围选择
    bool isShiftPressed = IsKeyAndButtonPressed(VK_SHIFT);
    
    // 如果没有按修饰键，清空现有选择集
    if (!isCtrlPressed && !isShiftPressed)
    {
        ClearSelection(pCurrentView->GetUiDocument()->GetDbDocument());
    }
    
    // 更新候选项
    UpdateCandidates(pCurrentView, pos);
    
    // 处理选择逻辑
    // ...
}
```

### 2. CAD 风格选择

```cpp
class GbmpPickActionUtil
{
public:
    static bool IS_CAD_STYLE_SELECT;  // CAD 风格选择标志
};

// CAD 风格选择的特殊处理
bool isCADStyleRectSelect = !(m_status & PS_GBMP_LBUTTON_DOWN) && 
                           GbmpPickActionUtil::IS_CAD_STYLE_SELECT;
```

## 框选逻辑

### 1. 框选状态判断

```cpp
bool GbmpPickActionBase::OnMovePoint(IUiView* pCurrentView, const Vector3d& pos)
{
    bool isCADStyleRectSelect = !(m_status & PS_GBMP_LBUTTON_DOWN) && 
                               GbmpPickActionUtil::IS_CAD_STYLE_SELECT;
    
    // 判断是否进入框选模式
    if (((m_status & PS_GBMP_LBUTTON_DOWN) && !GbmpPickActionUtil::IS_CAD_STYLE_SELECT) || 
        isCADStyleRectSelect)
    {
        // 如果已经选中物体且未按住ctrl或shift键，则不允许框选
        if ((!IsSelectionEmpty() &&
            !IsKeyAndButtonPressed(VK_CONTROL) &&
            !GbmpPickActionUtil::IS_CAD_STYLE_SELECT &&
            !IsKeyAndButtonPressed(VK_SHIFT)) ||
            (IsDraggingAwaySelectedElementsEnabled(pCurrentView)))
        {
            return true;
        }
        
        // 绘制框选矩形
        DrawSelectRectangle(pCurrentView, pos, isReversePick);
    }
}
```

### 2. 框选结果处理

```cpp
bool GbmpPickActionBase::OnLButtonUp(IUiView* pCurrentView, const Vector3d& pos)
{
    // 获取框选结果
    bool isReversePick = false;
    OwnerPtr<IPickResult> pickResults = GetPickResults(pCurrentView, pos, isReversePick);
    
    if (!pickResults->IsEmpty())
    {
        std::vector<OwnerPtr<IPick>>& picks = pickResults->GetAllPicks();
        GraphicsNodeReferenceOwnerPtrVector& pickData = picks.at(0)->GetAllGraphicsNodeReferencesFw();
        
        // 过滤辅助对象
        RemoveAuxiliaryElementHelper removeHelper(dynamic_cast<IDocument*>(pDoc));
        OwnerPtrContainerUtil::RemoveItemsIf(pickData, removeHelper);
        
        // 检查编辑权限并添加到选择集
        GraphicsNodeReferenceOwnerPtrSet pickSets;
        IEditMode* pEditModeUE = IEditMode::GetTopActiveEditMode(pDoc);
        
        for (auto iter = pickData.begin(); iter != pickData.end(); ++iter)
        {
            if (IsAuxiliaryElement(pDoc, (*iter)->GetElementId()))
                continue;
                
            const IElement* pElement = pDoc->GetElement((*iter)->GetElementId());
            if (pEditModeUE && !pEditModeUE->IsElementEditable(pElement))
                continue;
                
            OwnerPtrContainerUtil::AddItem(pickSets, **iter);
        }
        
        AddToSelectionGroup(pDoc, pickSets);
    }
    
    OnSelectionChanged();
}
```

## 辅助对象过滤

### 1. 辅助对象判断

```cpp
bool GbmpPickActionBase::IsAuxiliaryElement(const IDocument* pDoc, const ElementId& id)
{
    const IElement* pElement = pDoc->GetElement(id);
    if (!pElement)
        return false;
        
    // 检查是否为辅助对象
    // 具体判断逻辑...
    return false;
}
```

### 2. 编辑权限检查

```cpp
bool GbmpPickActionBase::IsCanBeSelect(IDocument* pDoc, const IGraphicsNodeReference& refAdd)
{
    IEditMode* pEditModeUE = IEditMode::GetTopActiveEditMode(pDoc);
    if (pEditModeUE)
    {
        const IElement* pElement = pDoc->GetElement(refAdd.GetElementId());
        if (!pEditModeUE->IsElementEditable(pElement))
            return false;
    }
    return true;
}
```

## 表格选择特殊处理

### 1. 表格检测

```cpp
void GbmpPickActionBase::HasTableInSelection(IDocument* pDoc, 
                                           const GraphicsNodeReferenceOwnerPtrSet& selections, 
                                           bool& hasTable) const
{
    hasTable = false;
    for (const auto& selection : selections)
    {
        const IElement* pElement = pDoc->GetElement(selection->GetElementId());
        if (pElement && IsTableElement(pElement))
        {
            hasTable = true;
            break;
        }
    }
}
```

### 2. 表格夹点处理

```cpp
// 表格选择时的特殊夹点处理
if (DEBUG_MODE(CtlrDontNeedTableItemGripPoints))
{
    // Ctrl 选择不需要生成单元格夹点
}

if (DEBUG_MODE(ShiftUpdateSelectionSelectionForTable))
{
    // Shift 按下处理表格选择集更新
}
```

## 性能优化

### 1. 预高亮优化

```cpp
bool GbmpPickActionBase::UpdateCandidates(IUiView* pCurrentView, const Vector3d& pos)
{
    // 保存旧的预高亮状态
    const GraphicsNodeReferenceOwnerPtrVector& preHighlights = IPreHighlights::Get()->GetAllGraphicsNodeReferences();
    GraphicsNodeReferenceOwnerPtrVector oldPreHighlights;
    OwnerPtrContainerUtil::AddItems(oldPreHighlights, preHighlights);
    
    // 更新候选项
    GbmpPickActionUtil::UpdateCandidatesSingleton(/* ... */);
    
    // 获取新的预高亮状态
    const GraphicsNodeReferenceOwnerPtrVector& newPreHighlights = IPreHighlights::Get()->GetAllGraphicsNodeReferences();
    
    // 只有当预高亮变化时，才刷新视图，否则非常耗性能！
    if (!OwnerPtrContainerUtil::IsSameContentContainer(oldPreHighlights, newPreHighlights))
    {
        UpdateView();
        return true;
    }
    
    return false;
}
```

### 2. 大场景优化

```cpp
// 大场景下的选择集处理优化
if (DEBUG_MODE(GrepSizeThresholdForLargeScene))
{
    // 设置大场景图形数量阈值
    pickContext->SetGrepSizeThresholdForLargeScene(1000);
}
```

## 调试支持

### 1. 调试模式

```cpp
CREATE_DEBUG_MODE(CtlrDontNeedTableItemGripPoints, 
    L"Ctrl选择不需要生成单元格夹点", 
    gcmp::DebugModeGroup::DMGT_GREP_AND_PICKSNAP, 
    L"GDMPLab", L"2023-12-20");

CREATE_DEBUG_MODE(ShiftUpdateSelectionSelectionForTable, 
    L"Shift按下处理表格选择集更新", 
    gcmp::DebugModeGroup::DMGT_GREP_AND_PICKSNAP, 
    L"GDMPLab", L"2023-12-20");
```

### 2. 状态栏信息显示

```cpp
std::wstring GbmpPickActionBase::GetPromptMessage() const
{
    IDocument* pDocument = GetDoc();
    const GraphicsNodeReferenceOwnerPtrVector& curCandidate = 
        IPickCandidates::Get()->GetCurrentPick()->GetAllGraphicsNodeReferences();
   
    if (!pDocument || curCandidate.empty())
        return ActionBase::GetPromptMessage();
    
    // 显示当前候选项信息
    std::wstring msg;
    if (!DEBUG_MODE(GBMPDontPrintCandidateOnStatusBar))
        msg += curCandidate.at(0)->GetDescriptionString();
    
    return msg;
}
