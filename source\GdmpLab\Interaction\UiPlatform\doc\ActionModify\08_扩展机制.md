# ActionModify 扩展机制

## 概述

ActionModify 采用了高度可扩展的架构设计，通过多种扩展机制支持定制化的修改行为。主要的扩展机制包括：Behavior 行为扩展、EventHandler 事件处理器扩展、Filter 过滤器扩展。这些扩展机制基于策略模式、责任链模式等设计模式实现。

## 扩展机制架构

### 1. 扩展组件关系图

```mermaid
classDiagram
    class GbmpActionModify {
        -m_opModifyElementsBehavior : OwnerPtr~IActionModifyBehavior~
        -m_opPickEventHandlers : vector~OwnerPtr~IPickEventHandler~~
        -m_oFilterForLocalSnap : OwnerPtr~IPickFilter~
        -m_oFilterForRemoteSnap : OwnerPtr~IPickFilter~
        +SetModifyElementsBehavior(behavior)
        +AddPickPostProcessEventHandler(handler)
        +SetFilterForLocalSnap(filter)
        +SetFilterForRemoteSnap(filter)
    }
    
    class IActionModifyBehavior {
        <<interface>>
        +CreateElementShadow(view, elementId) IElement*
        +ModifyElement(view, elementId, origin, moveVector) bool
        +ModifyGripPoint(view, elementId, origin, moveVector) bool
        +PrepareContextMenu(view) OwnerPtr~IMenuItemContainerDefinition~
        +OnLButtonDoubleClick(view, pos, elementId) bool
        +ProcessPostModify(view, elementIds, snapCandidates)
    }
    
    class IPickEventHandler {
        <<interface>>
        +On(args) void
    }
    
    class IPickFilter {
        <<interface>>
        +AllowElement(elementId) bool
        +AllowGraphicsNode(nodeRef) bool
        +SetPickTargetOption(target) bool
    }
    
    class GbmpModifyElementsBehavior {
        +CreateElementShadow() IElement*
        +ModifyElement() bool
        +ModifyGripPoint() bool
        +PrepareContextMenu() OwnerPtr~IMenuItemContainerDefinition~
    }
    
    class CustomBehavior {
        +CreateElementShadow() IElement*
        +ModifyElement() bool
        +ModifyGripPoint() bool
    }
    
    class CustomEventHandler {
        +On(args) void
    }
    
    class CustomPickFilter {
        +AllowElement(elementId) bool
        +AllowGraphicsNode(nodeRef) bool
    }
    
    GbmpActionModify --> IActionModifyBehavior : uses
    GbmpActionModify --> IPickEventHandler : uses
    GbmpActionModify --> IPickFilter : uses
    
    IActionModifyBehavior <|.. GbmpModifyElementsBehavior
    IActionModifyBehavior <|.. CustomBehavior
    IPickEventHandler <|.. CustomEventHandler
    IPickFilter <|.. CustomPickFilter
```

## Behavior 行为扩展

### 1. IActionModifyBehavior 接口

```cpp
class IActionModifyBehavior
{
public:
    virtual ~IActionModifyBehavior() {}
    
    // 核心修改方法
    virtual IElement* CreateElementShadow(
        IUiView* pCurrentView, 
        const ElementId& modifiedElementId) = 0;
    
    virtual bool ModifyElement(
        const IUiView* pCurrentView, 
        const ElementId& modifiedElementId, 
        const Vector3d& originPoint, 
        const Vector3d& moveVector) = 0;
    
    virtual bool ModifyGripPoint(
        IUiView* pCurrentView, 
        const ElementId& modifiedElementId, 
        const Vector3d& originPoint, 
        const Vector3d& moveVector) = 0;
    
    // 交互方法
    virtual OwnerPtr<IMenuItemContainerDefinition> PrepareContextMenu(
        IUiView* pCurrentView) = 0;
    
    virtual bool OnLButtonDoubleClick(
        IUiView* pCurrentView, 
        const Vector3d& pos, 
        const ElementId& elementId) = 0;
    
    // 后处理方法
    virtual void ProcessPostModify(
        IUiView* pCurrentView, 
        const std::vector<ElementId>& modifiedElementIds, 
        const ISnapCandidates* pSnapCandidates) = 0;
};
```

### 2. 自定义 Behavior 实现

```cpp
class CustomModifyBehavior : public IActionModifyBehavior
{
public:
    // 创建影子对象
    virtual IElement* CreateElementShadow(
        IUiView* pCurrentView, 
        const ElementId& modifiedElementId) override
    {
        IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();
        IElement* pOriginalElement = pDoc->GetElement(modifiedElementId);
        
        if (!pOriginalElement)
            return nullptr;
        
        // 创建影子对象的自定义逻辑
        IElement* pShadowElement = CreateShadowElement(pOriginalElement);
        
        // 设置影子对象属性
        if (pShadowElement)
        {
            SetShadowElementProperties(pShadowElement);
        }
        
        return pShadowElement;
    }
    
    // 修改图元
    virtual bool ModifyElement(
        const IUiView* pCurrentView, 
        const ElementId& modifiedElementId, 
        const Vector3d& originPoint, 
        const Vector3d& moveVector) override
    {
        IDocument* pDoc = pCurrentView->GetUiDocument()->GetDbDocument();
        IElement* pElement = pDoc->GetElement(modifiedElementId);
        
        if (!pElement)
            return false;
        
        // 自定义修改逻辑
        return ApplyCustomModification(pElement, originPoint, moveVector);
    }
    
    // 修改夹点
    virtual bool ModifyGripPoint(
        IUiView* pCurrentView, 
        const ElementId& modifiedElementId, 
        const Vector3d& originPoint, 
        const Vector3d& moveVector) override
    {
        // 自定义夹点修改逻辑
        return HandleGripPointModification(
            pCurrentView, modifiedElementId, originPoint, moveVector);
    }
    
    // 准备上下文菜单
    virtual OwnerPtr<IMenuItemContainerDefinition> PrepareContextMenu(
        IUiView* pCurrentView) override
    {
        // 创建自定义上下文菜单
        OwnerPtr<IMenuItemContainerDefinition> pMenu = 
            IMenuItemContainerDefinition::Create(L"CustomMenu", L"");
        
        // 添加自定义菜单项
        AddCustomMenuItems(pMenu.get());
        
        return pMenu;
    }
    
    // 双击处理
    virtual bool OnLButtonDoubleClick(
        IUiView* pCurrentView, 
        const Vector3d& pos, 
        const ElementId& elementId) override
    {
        // 自定义双击处理逻辑
        return HandleCustomDoubleClick(pCurrentView, pos, elementId);
    }
    
    // 后处理
    virtual void ProcessPostModify(
        IUiView* pCurrentView, 
        const std::vector<ElementId>& modifiedElementIds, 
        const ISnapCandidates* pSnapCandidates) override
    {
        // 自定义后处理逻辑
        PerformCustomPostProcessing(pCurrentView, modifiedElementIds, pSnapCandidates);
    }

private:
    IElement* CreateShadowElement(const IElement* pOriginalElement);
    void SetShadowElementProperties(IElement* pShadowElement);
    bool ApplyCustomModification(IElement* pElement, const Vector3d& origin, const Vector3d& move);
    bool HandleGripPointModification(IUiView* pView, const ElementId& id, const Vector3d& origin, const Vector3d& move);
    void AddCustomMenuItems(IMenuItemContainerDefinition* pMenu);
    bool HandleCustomDoubleClick(IUiView* pView, const Vector3d& pos, const ElementId& id);
    void PerformCustomPostProcessing(IUiView* pView, const std::vector<ElementId>& ids, const ISnapCandidates* pSnap);
};
```

### 3. Behavior 设置和使用

```cpp
// 在 ActionModify 中设置自定义 Behavior
void SetupCustomBehavior(GbmpActionModify* pActionModify)
{
    OwnerPtr<CustomModifyBehavior> pCustomBehavior = 
        OwnerPtr<CustomModifyBehavior>::Create();
    
    pActionModify->SetModifyElementsBehavior(
        TransferOwnership(pCustomBehavior));
}

// 在 ActionModifyInput 中配置
ActionModifyInput input;
input.SetModifyElementsBehavior(
    OwnerPtr<CustomModifyBehavior>::Create());
```

## EventHandler 事件处理器扩展

### 1. IPickEventHandler 接口

```cpp
class IPickEventHandler : public WeakReferenceable
{
public:
    // 事件处理入口函数
    virtual void On(IPickEventArgs* pArgs) = 0;
};
```

### 2. 自定义事件处理器实现

```cpp
class CustomPickEventHandler : public IPickEventHandler
{
public:
    virtual void On(IPickEventArgs* pArgs) override
    {
        if (!pArgs)
            return;
        
        // 获取事件类型
        PickEventType eventType = pArgs->GetEventType();
        
        switch (eventType)
        {
        case PickEventType::BeforePick:
            HandleBeforePick(pArgs);
            break;
            
        case PickEventType::AfterPick:
            HandleAfterPick(pArgs);
            break;
            
        case PickEventType::PickCancelled:
            HandlePickCancelled(pArgs);
            break;
            
        default:
            break;
        }
    }

private:
    void HandleBeforePick(IPickEventArgs* pArgs)
    {
        // 拾取前的自定义处理
        IPickTarget* pTarget = pArgs->GetPickTarget();
        
        // 修改拾取目标或添加额外逻辑
        CustomizePickTarget(pTarget);
    }
    
    void HandleAfterPick(IPickEventArgs* pArgs)
    {
        // 拾取后的自定义处理
        const std::vector<ElementId>& pickedElements = pArgs->GetPickedElements();
        
        // 对拾取结果进行后处理
        ProcessPickedElements(pickedElements);
    }
    
    void HandlePickCancelled(IPickEventArgs* pArgs)
    {
        // 拾取取消的处理
        CleanupPickOperation();
    }
    
    void CustomizePickTarget(IPickTarget* pTarget);
    void ProcessPickedElements(const std::vector<ElementId>& elements);
    void CleanupPickOperation();
};
```

### 3. 事件处理器注册和使用

```cpp
// 注册拾取后处理事件处理器
void RegisterPickEventHandlers(GbmpActionModify* pActionModify)
{
    // 创建自定义事件处理器
    OwnerPtr<CustomPickEventHandler> pEventHandler = 
        OwnerPtr<CustomPickEventHandler>::Create();
    
    // 添加到 ActionModify
    pActionModify->AddPickPostProcessEventHandler(
        TransferOwnership(pEventHandler));
}

// 注册矩形拾取事件处理器
void RegisterRectPickEventHandlers(GbmpActionModify* pActionModify)
{
    OwnerPtr<CustomRectPickEventHandler> pRectEventHandler = 
        OwnerPtr<CustomRectPickEventHandler>::Create();
    
    pActionModify->AddRectPickPostProcessEventHandler(
        TransferOwnership(pRectEventHandler));
}
```

## Filter 过滤器扩展

### 1. IPickFilter 接口

```cpp
class IPickFilter : public IObject
{
public:
    // 设置是否允许拾取此元素的图形表达及其子节点
    virtual bool AllowElement(const ElementId& elementId) const = 0;
    
    // 设置是否允许拾取该图形节点
    virtual bool AllowGraphicsNode(
        const IGraphicsNodeReference& graphicsNodeReference) const = 0;
    
    // 设置拾取目标
    virtual bool SetPickTargetOption(IPickTarget* pickTarget) = 0;
};
```

### 2. 自定义过滤器实现

```cpp
class CustomPickFilter : public IPickFilter
{
public:
    CustomPickFilter(const std::set<ElementId>& allowedElements)
        : m_allowedElements(allowedElements)
    {
    }
    
    virtual bool AllowElement(const ElementId& elementId) const override
    {
        // 基于白名单的过滤
        if (!m_allowedElements.empty())
        {
            return m_allowedElements.find(elementId) != m_allowedElements.end();
        }
        
        // 基于元素类型的过滤
        IDocument* pDoc = GetCurrentDocument();
        if (pDoc)
        {
            IElement* pElement = pDoc->GetElement(elementId);
            if (pElement)
            {
                return IsElementTypeAllowed(pElement);
            }
        }
        
        return true;
    }
    
    virtual bool AllowGraphicsNode(
        const IGraphicsNodeReference& graphicsNodeReference) const override
    {
        // 基于图形节点属性的过滤
        ElementId elementId = graphicsNodeReference.GetElementId();
        
        // 首先检查元素是否允许
        if (!AllowElement(elementId))
            return false;
        
        // 检查图形节点特定属性
        return IsGraphicsNodeAllowed(graphicsNodeReference);
    }
    
    virtual bool SetPickTargetOption(IPickTarget* pickTarget) override
    {
        if (!pickTarget)
            return false;
        
        // 配置拾取目标选项
        ConfigurePickTarget(pickTarget);
        
        return true;
    }

private:
    std::set<ElementId> m_allowedElements;
    
    bool IsElementTypeAllowed(const IElement* pElement) const
    {
        // 根据元素类型判断是否允许拾取
        if (const IWall* pWall = quick_cast<IWall>(pElement))
        {
            return true; // 允许拾取墙
        }
        else if (const IBeam* pBeam = quick_cast<IBeam>(pElement))
        {
            return false; // 不允许拾取梁
        }
        
        return true; // 默认允许
    }
    
    bool IsGraphicsNodeAllowed(const IGraphicsNodeReference& nodeRef) const
    {
        // 检查图形节点的可见性、层级等属性
        if (!nodeRef.IsVisible())
            return false;
        
        // 检查图形节点的类型
        GraphicsNodeType nodeType = nodeRef.GetNodeType();
        return IsNodeTypeAllowed(nodeType);
    }
    
    bool IsNodeTypeAllowed(GraphicsNodeType nodeType) const
    {
        // 根据节点类型过滤
        switch (nodeType)
        {
        case GraphicsNodeType::Solid:
            return true;
        case GraphicsNodeType::Curve:
            return true;
        case GraphicsNodeType::Text:
            return false; // 不允许拾取文本
        default:
            return true;
        }
    }
    
    void ConfigurePickTarget(IPickTarget* pickTarget) const
    {
        // 配置拾取目标的选项
        pickTarget->SetPickMode(PickMode::Element);
        pickTarget->SetPickTolerance(5); // 设置拾取容差
    }
    
    IDocument* GetCurrentDocument() const;
};
```

### 3. 过滤器设置和使用

```cpp
// 设置本地捕捉过滤器
void SetupLocalSnapFilter(GbmpActionModify* pActionModify)
{
    std::set<ElementId> allowedElements;
    // 添加允许捕捉的元素ID
    allowedElements.insert(elementId1);
    allowedElements.insert(elementId2);
    
    OwnerPtr<CustomPickFilter> pFilter = 
        OwnerPtr<CustomPickFilter>::Create(allowedElements);
    
    pActionModify->SetFilterForLocalSnap(TransferOwnership(pFilter));
}

// 设置远程捕捉过滤器
void SetupRemoteSnapFilter(GbmpActionModify* pActionModify)
{
    OwnerPtr<CustomPickFilter> pRemoteFilter = 
        OwnerPtr<CustomPickFilter>::Create(std::set<ElementId>());
    
    pActionModify->SetFilterForRemoteSnap(TransferOwnership(pRemoteFilter));
}

// 在 ActionModifyInput 中配置过滤器
ActionModifyInput input;
input.SetFilterForLocalSnap(OwnerPtr<CustomPickFilter>::Create());
input.SetFilterForRemoteSnap(OwnerPtr<CustomPickFilter>::Create());
```

## 扩展机制的组合使用

### 1. 完整的扩展配置

```cpp
class CompleteActionModifyExtension
{
public:
    static void ConfigureActionModify(GbmpActionModify* pActionModify)
    {
        // 1. 设置自定义行为
        SetupCustomBehavior(pActionModify);
        
        // 2. 注册事件处理器
        RegisterEventHandlers(pActionModify);
        
        // 3. 配置过滤器
        SetupFilters(pActionModify);
    }

private:
    static void SetupCustomBehavior(GbmpActionModify* pActionModify)
    {
        OwnerPtr<CustomModifyBehavior> pBehavior = 
            OwnerPtr<CustomModifyBehavior>::Create();
        
        pActionModify->SetModifyElementsBehavior(
            TransferOwnership(pBehavior));
    }
    
    static void RegisterEventHandlers(GbmpActionModify* pActionModify)
    {
        // 拾取后处理事件处理器
        OwnerPtr<CustomPickEventHandler> pPickHandler = 
            OwnerPtr<CustomPickEventHandler>::Create();
        pActionModify->AddPickPostProcessEventHandler(
            TransferOwnership(pPickHandler));
        
        // 矩形拾取事件处理器
        OwnerPtr<CustomRectPickEventHandler> pRectHandler = 
            OwnerPtr<CustomRectPickEventHandler>::Create();
        pActionModify->AddRectPickPostProcessEventHandler(
            TransferOwnership(pRectHandler));
    }
    
    static void SetupFilters(GbmpActionModify* pActionModify)
    {
        // 本地捕捉过滤器
        OwnerPtr<CustomPickFilter> pLocalFilter = 
            OwnerPtr<CustomPickFilter>::Create();
        pActionModify->SetFilterForLocalSnap(TransferOwnership(pLocalFilter));
        
        // 远程捕捉过滤器
        OwnerPtr<CustomPickFilter> pRemoteFilter = 
            OwnerPtr<CustomPickFilter>::Create();
        pActionModify->SetFilterForRemoteSnap(TransferOwnership(pRemoteFilter));
    }
};
```

### 2. 扩展机制的协作流程

```mermaid
sequenceDiagram
    participant User as 用户操作
    participant Action as GbmpActionModify
    participant Filter as IPickFilter
    participant Handler as IPickEventHandler
    participant Behavior as IActionModifyBehavior
    
    User->>Action: 鼠标点击
    Action->>Filter: AllowElement()
    Filter-->>Action: 过滤结果
    
    Action->>Handler: On(BeforePick)
    Handler-->>Action: 预处理完成
    
    Action->>Action: 执行拾取
    
    Action->>Handler: On(AfterPick)
    Handler-->>Action: 后处理完成
    
    User->>Action: 开始拖拽
    Action->>Behavior: CreateElementShadow()
    Behavior-->>Action: 影子对象
    
    User->>Action: 拖拽移动
    Action->>Behavior: ModifyElement()
    Behavior-->>Action: 修改结果
    
    User->>Action: 完成拖拽
    Action->>Behavior: ProcessPostModify()
    Behavior-->>Action: 后处理完成
```

## 扩展机制的最佳实践

### 1. 扩展设计原则

- **单一职责**: 每个扩展组件只负责特定的功能
- **开闭原则**: 对扩展开放，对修改封闭
- **依赖倒置**: 依赖抽象接口而不是具体实现
- **组合优于继承**: 通过组合方式实现功能扩展

### 2. 性能优化建议

- **延迟初始化**: 只在需要时创建扩展对象
- **缓存机制**: 缓存频繁使用的过滤结果
- **批量处理**: 批量处理多个元素的操作
- **内存管理**: 及时释放不再使用的扩展对象

### 3. 调试和测试

```cpp
// 扩展组件的调试支持
class DebugActionModifyExtension
{
public:
    static void EnableDebugMode(GbmpActionModify* pActionModify)
    {
        // 添加调试事件处理器
        OwnerPtr<DebugEventHandler> pDebugHandler = 
            OwnerPtr<DebugEventHandler>::Create();
        pActionModify->AddPickPostProcessEventHandler(
            TransferOwnership(pDebugHandler));
    }
};

class DebugEventHandler : public IPickEventHandler
{
public:
    virtual void On(IPickEventArgs* pArgs) override
    {
        // 输出调试信息
        PickEventType eventType = pArgs->GetEventType();
        std::vector<ElementId> elements = pArgs->GetPickedElements();
        
        DBG_INFO(L"Pick Event: Type=%d, Elements=%d", 
                 (int)eventType, (int)elements.size());
    }
};
```
